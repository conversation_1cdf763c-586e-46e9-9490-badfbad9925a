import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Space,
  Popconfirm,
  Tag,
  Card,
  Typography,
  Tooltip,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TableOutlined,
  LinkOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { feishuAppService, FeishuApp, FeishuAppCreate, FeishuAppUpdate, BitableCreateRequest } from '../services/feishuAppService';

const { Title, Text } = Typography;

const FeishuAppManage: React.FC = () => {
  const [apps, setApps] = useState<FeishuApp[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [bitableModalVisible, setBitableModalVisible] = useState(false);
  const [editingApp, setEditingApp] = useState<FeishuApp | null>(null);
  const [currentApp, setCurrentApp] = useState<FeishuApp | null>(null);
  const [form] = Form.useForm();
  const [bitableForm] = Form.useForm();

  // 加载飞书应用列表
  const loadApps = async () => {
    setLoading(true);
    try {
      const data = await feishuAppService.getFeishuApps();
      setApps(data);
    } catch (error: any) {
      message.error('加载飞书应用列表失败');
      console.error('Load apps error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApps();
  }, []);

  // 创建或更新飞书应用
  const handleSubmit = async (values: FeishuAppCreate | FeishuAppUpdate) => {
    try {
      if (editingApp) {
        await feishuAppService.updateFeishuApp(editingApp.id, values);
        message.success('飞书应用更新成功');
      } else {
        await feishuAppService.createFeishuApp(values as FeishuAppCreate);
        message.success('飞书应用创建成功');
      }
      setModalVisible(false);
      setEditingApp(null);
      form.resetFields();
      loadApps();
    } catch (error: any) {
      const errorMsg = error.response?.data?.detail || '操作失败';
      message.error(errorMsg);
    }
  };

  // 删除飞书应用
  const handleDelete = async (app: FeishuApp) => {
    try {
      await feishuAppService.deleteFeishuApp(app.id);
      message.success('飞书应用删除成功');
      loadApps();
    } catch (error: any) {
      const errorMsg = error.response?.data?.detail || '删除失败';
      message.error(errorMsg);
    }
  };

  // 创建多维表格
  const handleCreateBitable = async (values: BitableCreateRequest) => {
    if (!currentApp) return;
    
    try {
      await feishuAppService.createBitableForApp(currentApp.id, values);
      message.success('多维表格创建成功');
      setBitableModalVisible(false);
      setCurrentApp(null);
      bitableForm.resetFields();
      loadApps();
    } catch (error: any) {
      const errorMsg = error.response?.data?.detail || '创建多维表格失败';
      message.error(errorMsg);
    }
  };

  // 打开编辑模态框
  const openEditModal = (app: FeishuApp) => {
    setEditingApp(app);
    form.setFieldsValue({
      name: app.name,
      app_id: app.app_id,
      // 不显示app_secret，保护敏感信息
    });
    setModalVisible(true);
  };

  // 打开创建多维表格模态框
  const openBitableModal = (app: FeishuApp) => {
    setCurrentApp(app);
    bitableForm.setFieldsValue({
      bitable_name: `${app.name}_数据表`,
    });
    setBitableModalVisible(true);
  };

  const columns = [
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: FeishuApp) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.app_id}
          </Text>
        </Space>
      ),
    },
    {
      title: '多维表格状态',
      key: 'bitable_status',
      render: (_: any, record: FeishuApp) => (
        <Space direction="vertical" size={0}>
          {record.has_bitable ? (
            <>
              <Tag color="green">已创建</Tag>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.bitable_name}
              </Text>
            </>
          ) : (
            <Tag color="orange">未创建</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: FeishuApp) => (
        <Space size="middle">
          <Tooltip title="编辑应用">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          
          {!record.has_bitable ? (
            <Tooltip title="创建多维表格">
              <Button
                type="text"
                icon={<TableOutlined />}
                onClick={() => openBitableModal(record)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="查看多维表格">
              <Button
                type="text"
                icon={<LinkOutlined />}
                onClick={() => window.open(record.url, '_blank')}
              />
            </Tooltip>
          )}
          
          <Popconfirm
            title="确定删除这个飞书应用吗？"
            description="删除后将无法恢复，请谨慎操作。"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除应用">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>飞书应用管理</Title>
            <Text type="secondary">管理飞书应用配置和多维表格</Text>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingApp(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              新增飞书应用
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={apps}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑飞书应用模态框 */}
      <Modal
        title={editingApp ? '编辑飞书应用' : '新增飞书应用'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingApp(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="应用名称"
            name="name"
            rules={[{ required: true, message: '请输入应用名称' }]}
          >
            <Input placeholder="请输入应用名称" />
          </Form.Item>

          <Form.Item
            label="App ID"
            name="app_id"
            rules={[{ required: true, message: '请输入飞书应用ID' }]}
          >
            <Input placeholder="请输入飞书应用ID" />
          </Form.Item>

          <Form.Item
            label="App Secret"
            name="app_secret"
            rules={[
              { required: !editingApp, message: '请输入飞书应用密钥' }
            ]}
          >
            <Input.Password 
              placeholder={editingApp ? '留空表示不修改密钥' : '请输入飞书应用密钥'} 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingApp(null);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingApp ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建多维表格模态框 */}
      <Modal
        title="创建多维表格"
        open={bitableModalVisible}
        onCancel={() => {
          setBitableModalVisible(false);
          setCurrentApp(null);
          bitableForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={bitableForm}
          layout="vertical"
          onFinish={handleCreateBitable}
        >
          <Form.Item
            label="表格名称"
            name="bitable_name"
            rules={[{ required: true, message: '请输入表格名称' }]}
          >
            <Input placeholder="请输入多维表格名称" />
          </Form.Item>

          <Form.Item
            label="文件夹Token（可选）"
            name="folder_token"
          >
            <Input placeholder="请输入飞书文件夹Token，留空将创建在根目录" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setBitableModalVisible(false);
                setCurrentApp(null);
                bitableForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FeishuAppManage;
