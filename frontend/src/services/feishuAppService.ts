import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 飞书应用相关接口
export interface FeishuApp {
  id: number;
  name: string;
  app_id: string;
  user_id: number;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  bitable_name?: string;
  app_token?: string;
  folder_token?: string;
  url?: string;
  bitable_created_at?: string;
  has_bitable: boolean;
}

export interface FeishuAppCreate {
  name: string;
  app_id: string;
  app_secret: string;
}

export interface FeishuAppUpdate {
  name?: string;
  app_id?: string;
  app_secret?: string;
}

export interface BitableCreateRequest {
  bitable_name?: string;
  folder_token?: string;
}

export interface BitableCreateResponse {
  success: boolean;
  message: string;
  app_token: string;
  folder_token?: string;
  url?: string;
  bitable_name: string;
}

export const feishuAppService = {
  // 获取飞书应用列表
  async getFeishuApps(): Promise<FeishuApp[]> {
    const response = await api.get('/api/feishu-apps/');
    return response.data;
  },

  // 创建飞书应用
  async createFeishuApp(data: FeishuAppCreate): Promise<FeishuApp> {
    const response = await api.post('/api/feishu-apps/', data);
    return response.data;
  },

  // 获取飞书应用详情
  async getFeishuApp(id: number): Promise<FeishuApp> {
    const response = await api.get(`/api/feishu-apps/${id}`);
    return response.data;
  },

  // 更新飞书应用
  async updateFeishuApp(id: number, data: FeishuAppUpdate): Promise<FeishuApp> {
    const response = await api.put(`/api/feishu-apps/${id}`, data);
    return response.data;
  },

  // 删除飞书应用
  async deleteFeishuApp(id: number): Promise<{ message: string }> {
    const response = await api.delete(`/api/feishu-apps/${id}`);
    return response.data;
  },

  // 为飞书应用创建多维表格
  async createBitableForApp(id: number, data: BitableCreateRequest): Promise<BitableCreateResponse> {
    const response = await api.post(`/api/feishu-apps/${id}/create-bitable`, data);
    return response.data;
  },

  // 获取已创建多维表格的飞书应用列表
  async getFeishuAppsWithBitable(): Promise<FeishuApp[]> {
    const response = await api.get('/api/feishu-apps/with-bitable');
    return response.data;
  },
};

export default feishuAppService;
