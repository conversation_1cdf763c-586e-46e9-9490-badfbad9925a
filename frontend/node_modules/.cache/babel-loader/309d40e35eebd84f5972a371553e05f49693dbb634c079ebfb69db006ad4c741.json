{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, message, Space, Popconfirm, Tag, Card, Typography, Tooltip, Row, Col } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, TableOutlined, LinkOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { feishuAppService } from '../services/feishuAppService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst FeishuAppManage = () => {\n  _s();\n  const [apps, setApps] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [bitableModalVisible, setBitableModalVisible] = useState(false);\n  const [editingApp, setEditingApp] = useState(null);\n  const [currentApp, setCurrentApp] = useState(null);\n  const [form] = Form.useForm();\n  const [bitableForm] = Form.useForm();\n\n  // 加载飞书应用列表\n  const loadApps = async () => {\n    setLoading(true);\n    try {\n      const data = await feishuAppService.getFeishuApps();\n      setApps(data);\n    } catch (error) {\n      message.error('加载飞书应用列表失败');\n      console.error('Load apps error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadApps();\n  }, []);\n\n  // 创建或更新飞书应用\n  const handleSubmit = async values => {\n    try {\n      if (editingApp) {\n        await feishuAppService.updateFeishuApp(editingApp.id, values);\n        message.success('飞书应用更新成功');\n      } else {\n        await feishuAppService.createFeishuApp(values);\n        message.success('飞书应用创建成功');\n      }\n      setModalVisible(false);\n      setEditingApp(null);\n      form.resetFields();\n      loadApps();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMsg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 删除飞书应用\n  const handleDelete = async app => {\n    try {\n      await feishuAppService.deleteFeishuApp(app.id);\n      message.success('飞书应用删除成功');\n      loadApps();\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMsg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '删除失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 创建多维表格\n  const handleCreateBitable = async values => {\n    if (!currentApp) return;\n    try {\n      await feishuAppService.createBitableForApp(currentApp.id, values);\n      message.success('多维表格创建成功');\n      setBitableModalVisible(false);\n      setCurrentApp(null);\n      bitableForm.resetFields();\n      loadApps();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMsg = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '创建多维表格失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 打开编辑模态框\n  const openEditModal = app => {\n    setEditingApp(app);\n    form.setFieldsValue({\n      name: app.name,\n      app_id: app.app_id\n      // 不显示app_secret，保护敏感信息\n    });\n    setModalVisible(true);\n  };\n\n  // 打开创建多维表格模态框\n  const openBitableModal = app => {\n    setCurrentApp(app);\n    bitableForm.setFieldsValue({\n      bitable_name: `${app.name}_数据表`\n    });\n    setBitableModalVisible(true);\n  };\n  const columns = [{\n    title: '应用名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: [\"ID: \", record.app_id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '多维表格状态',\n    key: 'bitable_status',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: record.has_bitable ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          children: \"\\u5DF2\\u521B\\u5EFA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: '12px'\n          },\n          children: record.bitable_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"orange\",\n        children: \"\\u672A\\u521B\\u5EFA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\\u5E94\\u7528\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 21\n          }, this),\n          onClick: () => openEditModal(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), !record.has_bitable ? /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u521B\\u5EFA\\u591A\\u7EF4\\u8868\\u683C\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(TableOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 23\n          }, this),\n          onClick: () => openBitableModal(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u591A\\u7EF4\\u8868\\u683C\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(LinkOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 23\n          }, this),\n          onClick: () => window.open(record.url, '_blank')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u98DE\\u4E66\\u5E94\\u7528\\u5417\\uFF1F\",\n        description: \"\\u5220\\u9664\\u540E\\u5C06\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            color: 'red'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 19\n        }, this),\n        onConfirm: () => handleDelete(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\\u5E94\\u7528\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            style: {\n              margin: 0\n            },\n            children: \"\\u98DE\\u4E66\\u5E94\\u7528\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u7BA1\\u7406\\u98DE\\u4E66\\u5E94\\u7528\\u914D\\u7F6E\\u548C\\u591A\\u7EF4\\u8868\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 21\n            }, this),\n            onClick: () => {\n              setEditingApp(null);\n              form.resetFields();\n              setModalVisible(true);\n            },\n            children: \"\\u65B0\\u589E\\u98DE\\u4E66\\u5E94\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: apps,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingApp ? '编辑飞书应用' : '新增飞书应用',\n      open: modalVisible,\n      onCancel: () => {\n        setModalVisible(false);\n        setEditingApp(null);\n        form.resetFields();\n      },\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5E94\\u7528\\u540D\\u79F0\",\n          name: \"name\",\n          rules: [{\n            required: true,\n            message: '请输入应用名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E94\\u7528\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"App ID\",\n          name: \"app_id\",\n          rules: [{\n            required: true,\n            message: '请输入飞书应用ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u98DE\\u4E66\\u5E94\\u7528ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"App Secret\",\n          name: \"app_secret\",\n          rules: [{\n            required: !editingApp,\n            message: '请输入飞书应用密钥'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: editingApp ? '留空表示不修改密钥' : '请输入飞书应用密钥'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setModalVisible(false);\n                setEditingApp(null);\n                form.resetFields();\n              },\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingApp ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u521B\\u5EFA\\u591A\\u7EF4\\u8868\\u683C\",\n      open: bitableModalVisible,\n      onCancel: () => {\n        setBitableModalVisible(false);\n        setCurrentApp(null);\n        bitableForm.resetFields();\n      },\n      footer: null,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: bitableForm,\n        layout: \"vertical\",\n        onFinish: handleCreateBitable,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u8868\\u683C\\u540D\\u79F0\",\n          name: \"bitable_name\",\n          rules: [{\n            required: true,\n            message: '请输入表格名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u591A\\u7EF4\\u8868\\u683C\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u6587\\u4EF6\\u5939Token\\uFF08\\u53EF\\u9009\\uFF09\",\n          name: \"folder_token\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u98DE\\u4E66\\u6587\\u4EF6\\u5939Token\\uFF0C\\u7559\\u7A7A\\u5C06\\u521B\\u5EFA\\u5728\\u6839\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setBitableModalVisible(false);\n                setCurrentApp(null);\n                bitableForm.resetFields();\n              },\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: \"\\u521B\\u5EFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(FeishuAppManage, \"ID3vfApcQHvxHYU9vfO6bPN/b5E=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = FeishuAppManage;\nexport default FeishuAppManage;\nvar _c;\n$RefreshReg$(_c, \"FeishuAppManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "message", "Space", "Popconfirm", "Tag", "Card", "Typography", "<PERSON><PERSON><PERSON>", "Row", "Col", "PlusOutlined", "EditOutlined", "DeleteOutlined", "TableOutlined", "LinkOutlined", "ExclamationCircleOutlined", "feishuAppService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "FeishuAppManage", "_s", "apps", "setApps", "loading", "setLoading", "modalVisible", "setModalVisible", "bitableModalVisible", "setBitableModalVisible", "editingApp", "setEditingApp", "currentApp", "setCurrentApp", "form", "useForm", "bitableForm", "loadApps", "data", "getFeishuApps", "error", "console", "handleSubmit", "values", "updateFeishuApp", "id", "success", "createFeishuApp", "resetFields", "_error$response", "_error$response$data", "errorMsg", "response", "detail", "handleDelete", "app", "deleteFeishuApp", "_error$response2", "_error$response2$data", "handleCreateBitable", "createBitableForApp", "_error$response3", "_error$response3$data", "openEditModal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "app_id", "openBitableModal", "bitable_name", "columns", "title", "dataIndex", "key", "render", "text", "record", "direction", "size", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "style", "fontSize", "_", "has_bitable", "color", "Date", "toLocaleString", "icon", "onClick", "window", "open", "url", "description", "onConfirm", "okText", "cancelText", "danger", "justify", "align", "marginBottom", "level", "margin", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "onCancel", "footer", "width", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "Password", "textAlign", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  message,\n  Space,\n  Popconfirm,\n  Tag,\n  Card,\n  Typography,\n  Tooltip,\n  Row,\n  Col,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  TableOutlined,\n  LinkOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport { feishuAppService, FeishuApp, FeishuAppCreate, FeishuAppUpdate, BitableCreateRequest } from '../services/feishuAppService';\n\nconst { Title, Text } = Typography;\n\nconst FeishuAppManage: React.FC = () => {\n  const [apps, setApps] = useState<FeishuApp[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [bitableModalVisible, setBitableModalVisible] = useState(false);\n  const [editingApp, setEditingApp] = useState<FeishuApp | null>(null);\n  const [currentApp, setCurrentApp] = useState<FeishuApp | null>(null);\n  const [form] = Form.useForm();\n  const [bitableForm] = Form.useForm();\n\n  // 加载飞书应用列表\n  const loadApps = async () => {\n    setLoading(true);\n    try {\n      const data = await feishuAppService.getFeishuApps();\n      setApps(data);\n    } catch (error: any) {\n      message.error('加载飞书应用列表失败');\n      console.error('Load apps error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadApps();\n  }, []);\n\n  // 创建或更新飞书应用\n  const handleSubmit = async (values: FeishuAppCreate | FeishuAppUpdate) => {\n    try {\n      if (editingApp) {\n        await feishuAppService.updateFeishuApp(editingApp.id, values);\n        message.success('飞书应用更新成功');\n      } else {\n        await feishuAppService.createFeishuApp(values as FeishuAppCreate);\n        message.success('飞书应用创建成功');\n      }\n      setModalVisible(false);\n      setEditingApp(null);\n      form.resetFields();\n      loadApps();\n    } catch (error: any) {\n      const errorMsg = error.response?.data?.detail || '操作失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 删除飞书应用\n  const handleDelete = async (app: FeishuApp) => {\n    try {\n      await feishuAppService.deleteFeishuApp(app.id);\n      message.success('飞书应用删除成功');\n      loadApps();\n    } catch (error: any) {\n      const errorMsg = error.response?.data?.detail || '删除失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 创建多维表格\n  const handleCreateBitable = async (values: BitableCreateRequest) => {\n    if (!currentApp) return;\n    \n    try {\n      await feishuAppService.createBitableForApp(currentApp.id, values);\n      message.success('多维表格创建成功');\n      setBitableModalVisible(false);\n      setCurrentApp(null);\n      bitableForm.resetFields();\n      loadApps();\n    } catch (error: any) {\n      const errorMsg = error.response?.data?.detail || '创建多维表格失败';\n      message.error(errorMsg);\n    }\n  };\n\n  // 打开编辑模态框\n  const openEditModal = (app: FeishuApp) => {\n    setEditingApp(app);\n    form.setFieldsValue({\n      name: app.name,\n      app_id: app.app_id,\n      // 不显示app_secret，保护敏感信息\n    });\n    setModalVisible(true);\n  };\n\n  // 打开创建多维表格模态框\n  const openBitableModal = (app: FeishuApp) => {\n    setCurrentApp(app);\n    bitableForm.setFieldsValue({\n      bitable_name: `${app.name}_数据表`,\n    });\n    setBitableModalVisible(true);\n  };\n\n  const columns = [\n    {\n      title: '应用名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text: string, record: FeishuApp) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{text}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            ID: {record.app_id}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '多维表格状态',\n      key: 'bitable_status',\n      render: (_: any, record: FeishuApp) => (\n        <Space direction=\"vertical\" size={0}>\n          {record.has_bitable ? (\n            <>\n              <Tag color=\"green\">已创建</Tag>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                {record.bitable_name}\n              </Text>\n            </>\n          ) : (\n            <Tag color=\"orange\">未创建</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: FeishuApp) => (\n        <Space size=\"middle\">\n          <Tooltip title=\"编辑应用\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => openEditModal(record)}\n            />\n          </Tooltip>\n          \n          {!record.has_bitable ? (\n            <Tooltip title=\"创建多维表格\">\n              <Button\n                type=\"text\"\n                icon={<TableOutlined />}\n                onClick={() => openBitableModal(record)}\n              />\n            </Tooltip>\n          ) : (\n            <Tooltip title=\"查看多维表格\">\n              <Button\n                type=\"text\"\n                icon={<LinkOutlined />}\n                onClick={() => window.open(record.url, '_blank')}\n              />\n            </Tooltip>\n          )}\n          \n          <Popconfirm\n            title=\"确定删除这个飞书应用吗？\"\n            description=\"删除后将无法恢复，请谨慎操作。\"\n            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除应用\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={3} style={{ margin: 0 }}>飞书应用管理</Title>\n            <Text type=\"secondary\">管理飞书应用配置和多维表格</Text>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => {\n                setEditingApp(null);\n                form.resetFields();\n                setModalVisible(true);\n              }}\n            >\n              新增飞书应用\n            </Button>\n          </Col>\n        </Row>\n\n        <Table\n          columns={columns}\n          dataSource={apps}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      {/* 创建/编辑飞书应用模态框 */}\n      <Modal\n        title={editingApp ? '编辑飞书应用' : '新增飞书应用'}\n        open={modalVisible}\n        onCancel={() => {\n          setModalVisible(false);\n          setEditingApp(null);\n          form.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            label=\"应用名称\"\n            name=\"name\"\n            rules={[{ required: true, message: '请输入应用名称' }]}\n          >\n            <Input placeholder=\"请输入应用名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"App ID\"\n            name=\"app_id\"\n            rules={[{ required: true, message: '请输入飞书应用ID' }]}\n          >\n            <Input placeholder=\"请输入飞书应用ID\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"App Secret\"\n            name=\"app_secret\"\n            rules={[\n              { required: !editingApp, message: '请输入飞书应用密钥' }\n            ]}\n          >\n            <Input.Password \n              placeholder={editingApp ? '留空表示不修改密钥' : '请输入飞书应用密钥'} \n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setModalVisible(false);\n                setEditingApp(null);\n                form.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingApp ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 创建多维表格模态框 */}\n      <Modal\n        title=\"创建多维表格\"\n        open={bitableModalVisible}\n        onCancel={() => {\n          setBitableModalVisible(false);\n          setCurrentApp(null);\n          bitableForm.resetFields();\n        }}\n        footer={null}\n        width={500}\n      >\n        <Form\n          form={bitableForm}\n          layout=\"vertical\"\n          onFinish={handleCreateBitable}\n        >\n          <Form.Item\n            label=\"表格名称\"\n            name=\"bitable_name\"\n            rules={[{ required: true, message: '请输入表格名称' }]}\n          >\n            <Input placeholder=\"请输入多维表格名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"文件夹Token（可选）\"\n            name=\"folder_token\"\n          >\n            <Input placeholder=\"请输入飞书文件夹Token，留空将创建在根目录\" />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setBitableModalVisible(false);\n                setCurrentApp(null);\n                bitableForm.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                创建\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default FeishuAppManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,yBAAyB,QACpB,mBAAmB;AAC1B,SAASC,gBAAgB,QAA2E,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnI,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAElC,MAAMiB,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAc,EAAE,CAAC;EACjD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAmB,IAAI,CAAC;EACpE,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAmB,IAAI,CAAC;EACpE,MAAM,CAAC2C,IAAI,CAAC,GAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,CAAC,GAAGxC,IAAI,CAACuC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAME,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,IAAI,GAAG,MAAMzB,gBAAgB,CAAC0B,aAAa,CAAC,CAAC;MACnDhB,OAAO,CAACe,IAAI,CAAC;IACf,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnB1C,OAAO,CAAC0C,KAAK,CAAC,YAAY,CAAC;MAC3BC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDjC,SAAS,CAAC,MAAM;IACd6C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,YAAY,GAAG,MAAOC,MAAyC,IAAK;IACxE,IAAI;MACF,IAAIb,UAAU,EAAE;QACd,MAAMjB,gBAAgB,CAAC+B,eAAe,CAACd,UAAU,CAACe,EAAE,EAAEF,MAAM,CAAC;QAC7D7C,OAAO,CAACgD,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL,MAAMjC,gBAAgB,CAACkC,eAAe,CAACJ,MAAyB,CAAC;QACjE7C,OAAO,CAACgD,OAAO,CAAC,UAAU,CAAC;MAC7B;MACAnB,eAAe,CAAC,KAAK,CAAC;MACtBI,aAAa,CAAC,IAAI,CAAC;MACnBG,IAAI,CAACc,WAAW,CAAC,CAAC;MAClBX,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACnB,MAAMC,QAAQ,GAAG,EAAAF,eAAA,GAAAT,KAAK,CAACY,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,MAAM;MACvDvD,OAAO,CAAC0C,KAAK,CAACW,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG,MAAOC,GAAc,IAAK;IAC7C,IAAI;MACF,MAAM1C,gBAAgB,CAAC2C,eAAe,CAACD,GAAG,CAACV,EAAE,CAAC;MAC9C/C,OAAO,CAACgD,OAAO,CAAC,UAAU,CAAC;MAC3BT,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACnB,MAAMP,QAAQ,GAAG,EAAAM,gBAAA,GAAAjB,KAAK,CAACY,QAAQ,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,MAAM;MACvDvD,OAAO,CAAC0C,KAAK,CAACW,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAG,MAAOhB,MAA4B,IAAK;IAClE,IAAI,CAACX,UAAU,EAAE;IAEjB,IAAI;MACF,MAAMnB,gBAAgB,CAAC+C,mBAAmB,CAAC5B,UAAU,CAACa,EAAE,EAAEF,MAAM,CAAC;MACjE7C,OAAO,CAACgD,OAAO,CAAC,UAAU,CAAC;MAC3BjB,sBAAsB,CAAC,KAAK,CAAC;MAC7BI,aAAa,CAAC,IAAI,CAAC;MACnBG,WAAW,CAACY,WAAW,CAAC,CAAC;MACzBX,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAAqB,gBAAA,EAAAC,qBAAA;MACnB,MAAMX,QAAQ,GAAG,EAAAU,gBAAA,GAAArB,KAAK,CAACY,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvB,IAAI,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBT,MAAM,KAAI,UAAU;MAC3DvD,OAAO,CAAC0C,KAAK,CAACW,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMY,aAAa,GAAIR,GAAc,IAAK;IACxCxB,aAAa,CAACwB,GAAG,CAAC;IAClBrB,IAAI,CAAC8B,cAAc,CAAC;MAClBC,IAAI,EAAEV,GAAG,CAACU,IAAI;MACdC,MAAM,EAAEX,GAAG,CAACW;MACZ;IACF,CAAC,CAAC;IACFvC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwC,gBAAgB,GAAIZ,GAAc,IAAK;IAC3CtB,aAAa,CAACsB,GAAG,CAAC;IAClBnB,WAAW,CAAC4B,cAAc,CAAC;MACzBI,YAAY,EAAE,GAAGb,GAAG,CAACU,IAAI;IAC3B,CAAC,CAAC;IACFpC,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMwC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAiB,kBACtC5D,OAAA,CAAChB,KAAK;MAAC6E,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClC/D,OAAA,CAACI,IAAI;QAAC4D,MAAM;QAAAD,QAAA,EAAEJ;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BpE,OAAA,CAACI,IAAI;QAACiE,IAAI,EAAC,WAAW;QAACC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAR,QAAA,GAAC,MAC9C,EAACH,MAAM,CAACT,MAAM;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEb,KAAK,EAAE,QAAQ;IACfE,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAEA,CAACc,CAAM,EAAEZ,MAAiB,kBAChC5D,OAAA,CAAChB,KAAK;MAAC6E,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,EACjCH,MAAM,CAACa,WAAW,gBACjBzE,OAAA,CAAAE,SAAA;QAAA6D,QAAA,gBACE/D,OAAA,CAACd,GAAG;UAACwF,KAAK,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5BpE,OAAA,CAACI,IAAI;UAACiE,IAAI,EAAC,WAAW;UAACC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAR,QAAA,EAChDH,MAAM,CAACP;QAAY;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA,eACP,CAAC,gBAEHpE,OAAA,CAACd,GAAG;QAACwF,KAAK,EAAC,QAAQ;QAAAX,QAAA,EAAC;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAC7B;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGC,IAAY,IAAK,IAAIgB,IAAI,CAAChB,IAAI,CAAC,CAACiB,cAAc,CAAC;EAC1D,CAAC,EACD;IACErB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACc,CAAM,EAAEZ,MAAiB,kBAChC5D,OAAA,CAAChB,KAAK;MAAC8E,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClB/D,OAAA,CAACX,OAAO;QAACkE,KAAK,EAAC,0BAAM;QAAAQ,QAAA,eACnB/D,OAAA,CAACrB,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXQ,IAAI,eAAE7E,OAAA,CAACP,YAAY;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEA,CAAA,KAAM9B,aAAa,CAACY,MAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAET,CAACR,MAAM,CAACa,WAAW,gBAClBzE,OAAA,CAACX,OAAO;QAACkE,KAAK,EAAC,sCAAQ;QAAAQ,QAAA,eACrB/D,OAAA,CAACrB,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXQ,IAAI,eAAE7E,OAAA,CAACL,aAAa;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBU,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAACQ,MAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,gBAEVpE,OAAA,CAACX,OAAO;QAACkE,KAAK,EAAC,sCAAQ;QAAAQ,QAAA,eACrB/D,OAAA,CAACrB,MAAM;UACL0F,IAAI,EAAC,MAAM;UACXQ,IAAI,eAAE7E,OAAA,CAACJ,YAAY;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAACpB,MAAM,CAACqB,GAAG,EAAE,QAAQ;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CACV,eAEDpE,OAAA,CAACf,UAAU;QACTsE,KAAK,EAAC,0EAAc;QACpB2B,WAAW,EAAC,4FAAiB;QAC7BL,IAAI,eAAE7E,OAAA,CAACH,yBAAyB;UAACyE,KAAK,EAAE;YAAEI,KAAK,EAAE;UAAM;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7De,SAAS,EAAEA,CAAA,KAAM5C,YAAY,CAACqB,MAAM,CAAE;QACtCwB,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAtB,QAAA,eAEf/D,OAAA,CAACX,OAAO;UAACkE,KAAK,EAAC,0BAAM;UAAAQ,QAAA,eACnB/D,OAAA,CAACrB,MAAM;YACL0F,IAAI,EAAC,MAAM;YACXiB,MAAM;YACNT,IAAI,eAAE7E,OAAA,CAACN,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEpE,OAAA;IAAA+D,QAAA,gBACE/D,OAAA,CAACb,IAAI;MAAA4E,QAAA,gBACH/D,OAAA,CAACV,GAAG;QAACiG,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAClB,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG,CAAE;QAAA1B,QAAA,gBACtE/D,OAAA,CAACT,GAAG;UAAAwE,QAAA,gBACF/D,OAAA,CAACG,KAAK;YAACuF,KAAK,EAAE,CAAE;YAACpB,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAE,CAAE;YAAA5B,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrDpE,OAAA,CAACI,IAAI;YAACiE,IAAI,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNpE,OAAA,CAACT,GAAG;UAAAwE,QAAA,eACF/D,OAAA,CAACrB,MAAM;YACL0F,IAAI,EAAC,SAAS;YACdQ,IAAI,eAAE7E,OAAA,CAACR,YAAY;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBU,OAAO,EAAEA,CAAA,KAAM;cACb9D,aAAa,CAAC,IAAI,CAAC;cACnBG,IAAI,CAACc,WAAW,CAAC,CAAC;cAClBrB,eAAe,CAAC,IAAI,CAAC;YACvB,CAAE;YAAAmD,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA,CAACtB,KAAK;QACJ4E,OAAO,EAAEA,OAAQ;QACjBsC,UAAU,EAAErF,IAAK;QACjBsF,MAAM,EAAC,IAAI;QACXpF,OAAO,EAAEA,OAAQ;QACjBqF,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPpE,OAAA,CAACpB,KAAK;MACJ2E,KAAK,EAAExC,UAAU,GAAG,QAAQ,GAAG,QAAS;MACxCiE,IAAI,EAAErE,YAAa;MACnByF,QAAQ,EAAEA,CAAA,KAAM;QACdxF,eAAe,CAAC,KAAK,CAAC;QACtBI,aAAa,CAAC,IAAI,CAAC;QACnBG,IAAI,CAACc,WAAW,CAAC,CAAC;MACpB,CAAE;MACFoE,MAAM,EAAE,IAAK;MACbC,KAAK,EAAE,GAAI;MAAAvC,QAAA,eAEX/D,OAAA,CAACnB,IAAI;QACHsC,IAAI,EAAEA,IAAK;QACXoF,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7E,YAAa;QAAAoC,QAAA,gBAEvB/D,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZxD,IAAI,EAAC,MAAM;UACXyD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAgF,QAAA,eAEhD/D,OAAA,CAAClB,KAAK;YAAC+H,WAAW,EAAC;UAAS;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZpE,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UACRC,KAAK,EAAC,QAAQ;UACdxD,IAAI,EAAC,QAAQ;UACbyD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7H,OAAO,EAAE;UAAY,CAAC,CAAE;UAAAgF,QAAA,eAElD/D,OAAA,CAAClB,KAAK;YAAC+H,WAAW,EAAC;UAAW;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZpE,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UACRC,KAAK,EAAC,YAAY;UAClBxD,IAAI,EAAC,YAAY;UACjByD,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,CAAC7F,UAAU;YAAEhC,OAAO,EAAE;UAAY,CAAC,CAC/C;UAAAgF,QAAA,eAEF/D,OAAA,CAAClB,KAAK,CAACgI,QAAQ;YACbD,WAAW,EAAE9F,UAAU,GAAG,WAAW,GAAG;UAAY;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZpE,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UAACnC,KAAK,EAAE;YAAEmB,YAAY,EAAE,CAAC;YAAEsB,SAAS,EAAE;UAAQ,CAAE;UAAAhD,QAAA,eACxD/D,OAAA,CAAChB,KAAK;YAAA+E,QAAA,gBACJ/D,OAAA,CAACrB,MAAM;cAACmG,OAAO,EAAEA,CAAA,KAAM;gBACrBlE,eAAe,CAAC,KAAK,CAAC;gBACtBI,aAAa,CAAC,IAAI,CAAC;gBACnBG,IAAI,CAACc,WAAW,CAAC,CAAC;cACpB,CAAE;cAAA8B,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA,CAACrB,MAAM;cAAC0F,IAAI,EAAC,SAAS;cAAC2C,QAAQ,EAAC,QAAQ;cAAAjD,QAAA,EACrChD,UAAU,GAAG,IAAI,GAAG;YAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRpE,OAAA,CAACpB,KAAK;MACJ2E,KAAK,EAAC,sCAAQ;MACdyB,IAAI,EAAEnE,mBAAoB;MAC1BuF,QAAQ,EAAEA,CAAA,KAAM;QACdtF,sBAAsB,CAAC,KAAK,CAAC;QAC7BI,aAAa,CAAC,IAAI,CAAC;QACnBG,WAAW,CAACY,WAAW,CAAC,CAAC;MAC3B,CAAE;MACFoE,MAAM,EAAE,IAAK;MACbC,KAAK,EAAE,GAAI;MAAAvC,QAAA,eAEX/D,OAAA,CAACnB,IAAI;QACHsC,IAAI,EAAEE,WAAY;QAClBkF,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE5D,mBAAoB;QAAAmB,QAAA,gBAE9B/D,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZxD,IAAI,EAAC,cAAc;UACnByD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAgF,QAAA,eAEhD/D,OAAA,CAAClB,KAAK;YAAC+H,WAAW,EAAC;UAAW;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZpE,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UACRC,KAAK,EAAC,iDAAc;UACpBxD,IAAI,EAAC,cAAc;UAAAa,QAAA,eAEnB/D,OAAA,CAAClB,KAAK;YAAC+H,WAAW,EAAC;UAAyB;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEZpE,OAAA,CAACnB,IAAI,CAAC4H,IAAI;UAACnC,KAAK,EAAE;YAAEmB,YAAY,EAAE,CAAC;YAAEsB,SAAS,EAAE;UAAQ,CAAE;UAAAhD,QAAA,eACxD/D,OAAA,CAAChB,KAAK;YAAA+E,QAAA,gBACJ/D,OAAA,CAACrB,MAAM;cAACmG,OAAO,EAAEA,CAAA,KAAM;gBACrBhE,sBAAsB,CAAC,KAAK,CAAC;gBAC7BI,aAAa,CAAC,IAAI,CAAC;gBACnBG,WAAW,CAACY,WAAW,CAAC,CAAC;cAC3B,CAAE;cAAA8B,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA,CAACrB,MAAM;cAAC0F,IAAI,EAAC,SAAS;cAAC2C,QAAQ,EAAC,QAAQ;cAAAjD,QAAA,EAAC;YAEzC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAhVID,eAAyB;EAAA,QAOdxB,IAAI,CAACuC,OAAO,EACLvC,IAAI,CAACuC,OAAO;AAAA;AAAA6F,EAAA,GAR9B5G,eAAyB;AAkV/B,eAAeA,eAAe;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}