{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tag, Popconfirm, Image, Spin, Checkbox } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, LinkOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { feishuAppService } from '../services/feishuAppService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst AccountManage = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuBindModalVisible, setFeishuBindModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState(null);\n  const [feishuLoadingAccounts, setFeishuLoadingAccounts] = useState(new Set());\n  const [feishuSyncModalVisible, setFeishuSyncModalVisible] = useState(false);\n  const [feishuSyncForm] = Form.useForm();\n  const [feishuApps, setFeishuApps] = useState([]);\n  const [selectedFeishuAppId, setSelectedFeishuAppId] = useState(null);\n  const platformOptions = [{\n    value: 'wechat_mp',\n    label: '微信公众号'\n  }, {\n    value: 'wechat_service',\n    label: '微信服务号'\n  }, {\n    value: 'wechat_channels',\n    label: '视频号'\n  }, {\n    value: 'xiaohongshu',\n    label: '小红书'\n  }];\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = account => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败');\n    }\n  };\n  const handleLogin = async account => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n  const startLoginStatusPolling = accountId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const {\n          logged_in\n        } = response.data;\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = account => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    if (!account.feishu_app_id) {\n      message.warning('请先绑定飞书应用后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 30); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n    setDownloadModalVisible(true);\n  };\n  const handleDownloadSubmit = async values => {\n    if (!downloadAccount) return;\n    const selectedTypes = values.data_types;\n    if (!selectedTypes || selectedTypes.length === 0) {\n      message.error('请至少选择一种数据类型');\n      return;\n    }\n    setDownloadLoading(true);\n    setDownloadProgress('正在启动批量下载任务...');\n    try {\n      // 启动批量下载任务\n      const response = await api.post(`/wechat/batch-download-data/${downloadAccount.id}`, {\n        start_date: values.start_date,\n        end_date: values.end_date,\n        data_types: selectedTypes\n      });\n      if (response.data.success && response.data.task_id) {\n        const taskId = response.data.task_id;\n        setDownloadProgress('下载任务已启动，正在监控进度...');\n\n        // 开始轮询任务状态\n        const pollInterval = setInterval(async () => {\n          try {\n            const statusResponse = await api.get(`/wechat/download-task-status/${taskId}`);\n            const taskStatus = statusResponse.data.task_status;\n            if (taskStatus.status === 'running') {\n              setDownloadProgress(`正在下载 ${taskStatus.progress}/${taskStatus.total} 个文件 (${taskStatus.current_file || '准备中'})`);\n            } else if (taskStatus.status === 'completed') {\n              clearInterval(pollInterval);\n              const {\n                downloaded_files,\n                failed_files\n              } = taskStatus;\n              if (downloaded_files.length > 0) {\n                message.success(`批量下载完成！成功: ${downloaded_files.length} 个文件${failed_files.length > 0 ? `，失败: ${failed_files.length} 个文件` : ''}`);\n              }\n              if (failed_files.length === 0) {\n                setDownloadModalVisible(false);\n              } else {\n                console.error('下载失败的文件:', failed_files);\n              }\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            } else if (taskStatus.status === 'failed') {\n              clearInterval(pollInterval);\n              message.error(`下载任务失败: ${taskStatus.message}`);\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            }\n          } catch (error) {\n            console.error('查询任务状态失败:', error);\n          }\n        }, 2000); // 每2秒查询一次状态\n\n        // 5分钟后停止轮询\n        setTimeout(() => {\n          clearInterval(pollInterval);\n          if (downloadLoading) {\n            message.warning('下载任务超时，请手动刷新查看结果');\n            setDownloadLoading(false);\n            setDownloadProgress('');\n          }\n        }, 300000);\n      } else {\n        message.error('启动下载任务失败');\n        setDownloadLoading(false);\n        setDownloadProgress('');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('启动下载任务错误:', error);\n      message.error(`启动下载任务失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n      setDownloadLoading(false);\n      setDownloadProgress('');\n    }\n  };\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    setDownloadProgress('');\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/wechat/logout-all');\n      const {\n        success,\n        message: msg,\n        logout_results\n      } = response.data;\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter(r => r.success).length;\n        const totalCount = logout_results.length;\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const loadFeishuApps = async () => {\n    try {\n      const apps = await feishuAppService.getFeishuAppsWithBitable();\n      setFeishuApps(apps);\n    } catch (error) {\n      console.error('加载飞书应用失败:', error);\n      message.error('加载飞书应用列表失败');\n    }\n  };\n  const handleBindFeishuApp = async account => {\n    setFeishuAccount(account);\n    await loadFeishuApps();\n    setFeishuBindModalVisible(true);\n  };\n  const handleConfirmBindFeishuApp = async () => {\n    if (!feishuAccount || !selectedFeishuAppId) {\n      message.error('请选择飞书应用');\n      return;\n    }\n    try {\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n      const response = await api.post(`/feishu/bind-app/${feishuAccount.id}?feishu_app_id=${selectedFeishuAppId}`);\n      if (response.data.success) {\n        message.success('飞书应用绑定成功！');\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('绑定飞书应用失败');\n      }\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('绑定飞书应用失败:', error);\n      message.error(`绑定失败: ${((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || '未知错误'}`);\n    } finally {\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n  const handleSyncToFeishu = account => {\n    if (!account.feishu_app_id) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n\n    // 显示确认对话框\n    Modal.confirm({\n      title: '确认同步数据到飞书',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6B64\\u64CD\\u4F5C\\u5C06\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            paddingLeft: '20px',\n            margin: '10px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u4ECE\\u670D\\u52A1\\u5668\\u5B58\\u50A8\\u76EE\\u5F55\\u8BFB\\u53D6\\u5DF2\\u4E0B\\u8F7D\\u7684Excel\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5220\\u9664\\u98DE\\u4E66\\u8868\\u683C\\u4E2D\\u7684\\u5DF2\\u6709\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5C06\\u65B0\\u6570\\u636E\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\u591A\\u7EF4\\u8868\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#ff4d4f',\n            fontWeight: 'bold'\n          },\n          children: \"\\u8BF7\\u786E\\u4FDD\\u5DF2\\u5148\\u4E0B\\u8F7D\\u6570\\u636E\\u5230\\u670D\\u52A1\\u5668\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this),\n      okText: '确认同步',\n      cancelText: '取消',\n      onOk: () => {\n        // 直接开始同步，不需要显示日期选择模态框\n        handleDirectSync(account);\n      }\n    });\n  };\n  const handleDirectSync = async account => {\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(account.id));\n      const response = await api.post(`/feishu/sync-data/${account.id}`);\n      if (response.data.success) {\n        const {\n          sync_results\n        } = response.data;\n        message.success(`数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(account.id);\n        return newSet;\n      });\n    }\n  };\n  const handleFeishuSyncSubmit = async values => {\n    if (!feishuAccount) return;\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n      const response = await api.post(`/feishu/sync-data/${feishuAccount.id}`, {\n        begin_date: values.begin_date,\n        end_date: values.end_date\n      });\n      if (response.data.success) {\n        const {\n          sync_results,\n          data_summary\n        } = response.data;\n        message.success(`数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`);\n        setFeishuSyncModalVisible(false);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.detail) || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n  const handleFeishuSyncModalClose = () => {\n    setFeishuSyncModalVisible(false);\n    setFeishuAccount(null);\n    feishuSyncForm.resetFields();\n  };\n  const columns = [{\n    title: '账号名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '平台类型',\n    dataIndex: 'platform',\n    key: 'platform',\n    render: platform => {\n      const option = platformOptions.find(opt => opt.value === platform);\n      return option ? option.label : platform;\n    }\n  }, {\n    title: '登录状态',\n    dataIndex: 'login_status',\n    key: 'login_status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status ? 'green' : 'red',\n      children: status ? '已登录' : '未登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后登录时间',\n    dataIndex: 'last_login_time',\n    key: 'last_login_time',\n    render: time => time ? new Date(time).toLocaleString() : '-'\n  }, {\n    title: '飞书状态',\n    key: 'feishu_status',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Tag, {\n      color: record.feishu_app_id ? 'blue' : 'default',\n      children: record.feishu_app_id ? '已创建' : '未创建'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleLogin(record),\n        disabled: record.login_status,\n        children: \"\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDownload(record),\n        disabled: !record.login_status || !record.feishu_app_id,\n        children: \"\\u4E0B\\u8F7D\\u5230\\u670D\\u52A1\\u5668\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 11\n      }, this), !record.feishu_app_id ? /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(LinkOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleBindFeishuApp(record),\n        loading: feishuLoadingAccounts.has(record.id),\n        disabled: feishuLoadingAccounts.has(record.id),\n        children: \"\\u7ED1\\u5B9A\\u98DE\\u4E66\\u5E94\\u7528\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(CloudSyncOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleSyncToFeishu(record),\n        disabled: !record.login_status || feishuLoadingAccounts.has(record.id),\n        loading: feishuLoadingAccounts.has(record.id),\n        children: \"\\u540C\\u6B65\\u5230\\u98DE\\u4E66\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u8FD9\\u4E2A\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n        description: \"\\u6CE8\\u9500\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u626B\\u7801\\u767B\\u5F55\",\n        onConfirm: () => handleLogout(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          loading: logoutLoading === record.id,\n          disabled: !record.login_status || logoutLoading === record.id,\n          children: \"\\u6CE8\\u9500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8D26\\u53F7\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8D26\\u53F7\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u6240\\u6709\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n          description: \"\\u8FD9\\u5C06\\u6CE8\\u9500\\u5F53\\u524D\\u7528\\u6237\\u7684\\u6240\\u6709\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u8D26\\u53F7\\u767B\\u5F55\\u72B6\\u6001\",\n          onConfirm: handleLogoutAll,\n          okText: \"\\u786E\\u5B9A\",\n          cancelText: \"\\u53D6\\u6D88\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 23\n            }, this),\n            loading: loading,\n            disabled: accounts.length === 0 || !accounts.some(acc => acc.login_status),\n            children: \"\\u6CE8\\u9500\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 21\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u6DFB\\u52A0\\u8D26\\u53F7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: accounts,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAccount ? '编辑账号' : '添加账号',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8D26\\u53F7\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入账号名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D26\\u53F7\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"platform\",\n          label: \"\\u5E73\\u53F0\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择平台类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E73\\u53F0\\u7C7B\\u578B\",\n            children: platformOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `登录 ${loginAccount === null || loginAccount === void 0 ? void 0 : loginAccount.name}`,\n      open: loginModalVisible,\n      onCancel: handleLoginModalClose,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoginModalClose,\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => loginAccount && handleLogin(loginAccount),\n        disabled: loginLoading || loginStatus === 'success',\n        children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801\"\n      }, \"retry\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 11\n      }, this)],\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px 0'\n        },\n        children: [loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16\n            },\n            children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 13\n        }, this), loginStatus === 'scanning' && qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: qrCodeUrl,\n            alt: \"\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801\",\n            width: 200,\n            height: 200,\n            style: {\n              border: '1px solid #d9d9d9'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16,\n              color: '#1890ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this), \" \\u8BF7\\u4F7F\\u7528\\u5FAE\\u4FE1\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\\u767B\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: \"\\u4E8C\\u7EF4\\u7801\\u5C06\\u572830\\u79D2\\u540E\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this), loginStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#52c41a',\n              marginBottom: 16\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u6210\\u529F\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this), loginStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#ff4d4f',\n              marginBottom: 16\n            },\n            children: \"\\u2717\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this), loginStatus === 'waiting' && !loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {\n            style: {\n              fontSize: '48px',\n              color: '#d9d9d9',\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u51C6\\u5907\\u83B7\\u53D6\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `下载数据 - ${downloadAccount === null || downloadAccount === void 0 ? void 0 : downloadAccount.name}`,\n      open: downloadModalVisible,\n      onCancel: handleDownloadModalClose,\n      onOk: () => downloadForm.submit(),\n      okText: \"\\u4E0B\\u8F7D\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: downloadLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: downloadForm,\n        layout: \"vertical\",\n        onFinish: handleDownloadSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"start_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"data_types\",\n          label: \"\\u9009\\u62E9\\u6570\\u636E\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请至少选择一种数据类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            style: {\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_trend\",\n                children: \"\\u5185\\u5BB9\\u6570\\u636E\\u8D8B\\u52BF\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_source\",\n                children: \"\\u5185\\u5BB9\\u6D41\\u91CF\\u6765\\u6E90\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_detail\",\n                children: \"\\u5185\\u5BB9\\u5DF2\\u901A\\u77E5\\u5185\\u5BB9\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"user_channel\",\n                children: \"\\u7528\\u6237\\u6E20\\u9053\\u6784\\u6210\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), downloadProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#e6f7ff',\n            padding: '12px',\n            borderRadius: '6px',\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '14px',\n              color: '#1890ff'\n            },\n            children: downloadProgress\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u53EF\\u9009\\u62E9\\u591A\\u79CD\\u6570\\u636E\\u7C7B\\u578B\\u8FDB\\u884C\\u6279\\u91CF\\u4E0B\\u8F7D\\u5230\\u670D\\u52A1\\u5668\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u6587\\u4EF6\\u5C06\\u4FDD\\u5B58\\u5728\\u670D\\u52A1\\u5668\\u5B58\\u50A8\\u76EE\\u5F55\\u4E2D\\uFF0C\\u4E0D\\u4F1A\\u4E0B\\u8F7D\\u5230\\u672C\\u5730\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u91C7\\u7528\\u5F02\\u6B65\\u4EFB\\u52A1\\u6A21\\u5F0F\\uFF0C\\u652F\\u6301\\u5B9E\\u65F6\\u8FDB\\u5EA6\\u76D1\\u63A7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u591A\\u4E2A\\u6587\\u4EF6\\u4E0B\\u8F7D\\u65F6\\u4F1A\\u81EA\\u52A8\\u95F4\\u96942-5\\u79D2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u4E14\\u5DF2\\u7ED1\\u5B9A\\u98DE\\u4E66\\u5E94\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4E0B\\u8F7D\\u5B8C\\u6210\\u540E\\u53EF\\u4F7F\\u7528\\\"\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\\"\\u529F\\u80FD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `同步数据到飞书 - ${feishuAccount === null || feishuAccount === void 0 ? void 0 : feishuAccount.name}`,\n      open: feishuSyncModalVisible,\n      onCancel: handleFeishuSyncModalClose,\n      onOk: () => feishuSyncForm.submit(),\n      okText: \"\\u5F00\\u59CB\\u540C\\u6B65\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: feishuSyncForm,\n        layout: \"vertical\",\n        onFinish: handleFeishuSyncSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"begin_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u5C06\\u4E0B\\u8F7D\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u6570\\u636E\\u5E76\\u81EA\\u52A8\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\u591A\\u7EF4\\u8868\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4F1A\\u81EA\\u52A8\\u521B\\u5EFA\\u7528\\u6237\\u6982\\u51B5\\u548C\\u56FE\\u6587\\u5206\\u6790\\u4E24\\u4E2A\\u6570\\u636E\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u4E14\\u5DF2\\u521B\\u5EFA\\u98DE\\u4E66\\u8868\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u540C\\u6B65\\u65F6\\u95F4\\u53EF\\u80FD\\u8F83\\u957F\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 912,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `绑定飞书应用 - ${feishuAccount === null || feishuAccount === void 0 ? void 0 : feishuAccount.name}`,\n      open: feishuBindModalVisible,\n      onCancel: () => {\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n      },\n      onOk: handleConfirmBindFeishuApp,\n      okText: \"\\u786E\\u8BA4\\u7ED1\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false,\n      width: 500,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u8BF7\\u9009\\u62E9\\u8981\\u7ED1\\u5B9A\\u7684\\u98DE\\u4E66\\u5E94\\u7528\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u98DE\\u4E66\\u5E94\\u7528\",\n          value: selectedFeishuAppId,\n          onChange: setSelectedFeishuAppId,\n          showSearch: true,\n          optionFilterProp: \"children\",\n          children: feishuApps.map(app => /*#__PURE__*/_jsxDEV(Option, {\n            value: app.id,\n            children: [app.name, \" - \", app.bitable_name]\n          }, app.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f0f2f5',\n          padding: '12px',\n          borderRadius: '6px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BF4\\u660E\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '8px 0 0 0',\n            paddingLeft: '16px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u53EA\\u663E\\u793A\\u5DF2\\u521B\\u5EFA\\u591A\\u7EF4\\u8868\\u683C\\u7684\\u98DE\\u4E66\\u5E94\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u7ED1\\u5B9A\\u540E\\u5C06\\u5220\\u9664\\u8D26\\u53F7\\u539F\\u6709\\u7684\\u6570\\u636E\\u8868\\uFF0C\\u5E76\\u521B\\u5EFA\\u65B0\\u7684\\u6570\\u636E\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5982\\u9700\\u521B\\u5EFA\\u65B0\\u7684\\u98DE\\u4E66\\u5E94\\u7528\\uFF0C\\u8BF7\\u524D\\u5F80\\\"\\u98DE\\u4E66\\u5E94\\u7528\\u7BA1\\u7406\\\"\\u9875\\u9762\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 991,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 959,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 686,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountManage, \"WrXRqul703Pv3gBDlpl4yZhaBtA=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AccountManage;\nexport default AccountManage;\nvar _c;\n$RefreshReg$(_c, \"AccountManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Tag", "Popconfirm", "Image", "Spin", "Checkbox", "PlusOutlined", "EditOutlined", "DeleteOutlined", "LoginOutlined", "QrcodeOutlined", "DownloadOutlined", "LogoutOutlined", "CloudSyncOutlined", "LinkOutlined", "api", "feishuAppService", "jsxDEV", "_jsxDEV", "Option", "AccountManage", "_s", "accounts", "setAccounts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingAccount", "setEditingAccount", "form", "useForm", "loginModalVisible", "setLoginModalVisible", "loginAccount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "qrCodeUrl", "setQrCodeUrl", "loginLoading", "setLoginLoading", "loginStatus", "setLoginStatus", "downloadModalVisible", "setDownloadModalVisible", "downloadAccount", "setDownloadAccount", "downloadLoading", "setDownloadLoading", "downloadProgress", "setDownloadProgress", "downloadForm", "logoutLoading", "setLogoutLoading", "feishuBindModalVisible", "setFeishuBindModalVisible", "feishuAccount", "set<PERSON><PERSON><PERSON>A<PERSON>unt", "feishuLoadingAccounts", "setFeishuLoadingAccounts", "Set", "feishuSyncModalVisible", "setFeishuSyncModalVisible", "feishuSyncForm", "feishuApps", "setFeishuApps", "selectedFeishuAppId", "setSelectedFeishuAppId", "platformOptions", "value", "label", "fetchAccounts", "response", "get", "data", "error", "handleAdd", "resetFields", "handleEdit", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "delete", "success", "handleSubmit", "values", "put", "post", "_error$response", "_error$response$data", "detail", "handleLogin", "qrcode", "startLoginStatusPolling", "_error$response2", "_error$response2$data", "accountId", "pollInterval", "setInterval", "logged_in", "clearInterval", "console", "setTimeout", "warning", "handleLoginModalClose", "handleDownload", "login_status", "feishu_app_id", "today", "Date", "endDate", "setDate", "getDate", "startDate", "start_date", "toISOString", "split", "end_date", "data_types", "handleDownloadSubmit", "selectedTypes", "length", "task_id", "taskId", "statusResponse", "taskStatus", "task_status", "status", "progress", "total", "current_file", "downloaded_files", "failed_files", "_error$response3", "_error$response3$data", "handleDownloadModalClose", "handleLogout", "clear_saved_state", "name", "_error$response4", "_error$response4$data", "handleForceLogout", "_error$response5", "_error$response5$data", "handleLogoutAll", "msg", "logout_results", "successCount", "filter", "r", "totalCount", "_error$response6", "_error$response6$data", "loadFeishuApps", "apps", "getFeishuAppsWithBitable", "handleBindFeishuApp", "handleConfirmBindFeishuApp", "prev", "add", "_error$response7", "_error$response7$data", "newSet", "handleSyncToFeishu", "confirm", "title", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "paddingLeft", "margin", "color", "fontWeight", "okText", "cancelText", "onOk", "handleDirectSync", "sync_results", "user_data_synced", "article_data_synced", "_error$response8", "_error$response8$data", "handleFeishuSyncSubmit", "begin_date", "data_summary", "_error$response9", "_error$response9$data", "handleFeishuSyncModalClose", "columns", "dataIndex", "key", "render", "platform", "option", "find", "opt", "time", "toLocaleString", "_", "record", "size", "type", "icon", "onClick", "disabled", "has", "description", "onConfirm", "danger", "extra", "some", "acc", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "open", "onCancel", "submit", "layout", "onFinish", "<PERSON><PERSON>", "rules", "required", "placeholder", "map", "footer", "width", "textAlign", "padding", "marginTop", "src", "alt", "height", "border", "fontSize", "marginBottom", "confirmLoading", "Group", "display", "flexDirection", "gap", "background", "borderRadius", "onChange", "showSearch", "optionFilterProp", "app", "bitable_name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Popconfirm,\n  Image,\n  Spin,\n  Checkbox\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, LinkOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { feishuAppService, FeishuApp } from '../services/feishuAppService';\n\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  platform: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n  feishu_table_id?: string;\n  feishu_created_at?: string;\n  feishu_app_id?: number;\n}\n\nconst AccountManage: React.FC = () => {\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState<Account | null>(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState<Account | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState<string>('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState<number | null>(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuBindModalVisible, setFeishuBindModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState<Account | null>(null);\n  const [feishuLoadingAccounts, setFeishuLoadingAccounts] = useState<Set<number>>(new Set());\n  const [feishuSyncModalVisible, setFeishuSyncModalVisible] = useState(false);\n  const [feishuSyncForm] = Form.useForm();\n  const [feishuApps, setFeishuApps] = useState<FeishuApp[]>([]);\n  const [selectedFeishuAppId, setSelectedFeishuAppId] = useState<number | null>(null);\n\n  const platformOptions = [\n    { value: 'wechat_mp', label: '微信公众号' },\n    { value: 'wechat_service', label: '微信服务号' },\n    { value: 'wechat_channels', label: '视频号' },\n    { value: 'xiaohongshu', label: '小红书' },\n  ];\n\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (account: Account) => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleLogin = async (account: Account) => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n\n  const startLoginStatusPolling = (accountId: number) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const { logged_in } = response.data;\n\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = (account: Account) => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    if (!account.feishu_app_id) {\n      message.warning('请先绑定飞书应用后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 30); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n\n    setDownloadModalVisible(true);\n  };\n\n  const handleDownloadSubmit = async (values: any) => {\n    if (!downloadAccount) return;\n\n    const selectedTypes = values.data_types;\n    if (!selectedTypes || selectedTypes.length === 0) {\n      message.error('请至少选择一种数据类型');\n      return;\n    }\n\n    setDownloadLoading(true);\n    setDownloadProgress('正在启动批量下载任务...');\n\n    try {\n      // 启动批量下载任务\n      const response = await api.post(`/wechat/batch-download-data/${downloadAccount.id}`, {\n        start_date: values.start_date,\n        end_date: values.end_date,\n        data_types: selectedTypes\n      });\n\n      if (response.data.success && response.data.task_id) {\n        const taskId = response.data.task_id;\n        setDownloadProgress('下载任务已启动，正在监控进度...');\n\n        // 开始轮询任务状态\n        const pollInterval = setInterval(async () => {\n          try {\n            const statusResponse = await api.get(`/wechat/download-task-status/${taskId}`);\n            const taskStatus = statusResponse.data.task_status;\n\n            if (taskStatus.status === 'running') {\n              setDownloadProgress(\n                `正在下载 ${taskStatus.progress}/${taskStatus.total} 个文件 (${taskStatus.current_file || '准备中'})`\n              );\n            } else if (taskStatus.status === 'completed') {\n              clearInterval(pollInterval);\n              const { downloaded_files, failed_files } = taskStatus;\n\n              if (downloaded_files.length > 0) {\n                message.success(`批量下载完成！成功: ${downloaded_files.length} 个文件${failed_files.length > 0 ? `，失败: ${failed_files.length} 个文件` : ''}`);\n              }\n\n              if (failed_files.length === 0) {\n                setDownloadModalVisible(false);\n              } else {\n                console.error('下载失败的文件:', failed_files);\n              }\n\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            } else if (taskStatus.status === 'failed') {\n              clearInterval(pollInterval);\n              message.error(`下载任务失败: ${taskStatus.message}`);\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            }\n          } catch (error) {\n            console.error('查询任务状态失败:', error);\n          }\n        }, 2000); // 每2秒查询一次状态\n\n        // 5分钟后停止轮询\n        setTimeout(() => {\n          clearInterval(pollInterval);\n          if (downloadLoading) {\n            message.warning('下载任务超时，请手动刷新查看结果');\n            setDownloadLoading(false);\n            setDownloadProgress('');\n          }\n        }, 300000);\n\n      } else {\n        message.error('启动下载任务失败');\n        setDownloadLoading(false);\n        setDownloadProgress('');\n      }\n    } catch (error: any) {\n      console.error('启动下载任务错误:', error);\n      message.error(`启动下载任务失败: ${error.response?.data?.detail || error.message}`);\n      setDownloadLoading(false);\n      setDownloadProgress('');\n    }\n  };\n\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    setDownloadProgress('');\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error: any) {\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n      \n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n\n      const response = await api.get('/wechat/logout-all');\n\n      const { success, message: msg, logout_results } = response.data;\n\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter((r: any) => r.success).length;\n        const totalCount = logout_results.length;\n\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const loadFeishuApps = async () => {\n    try {\n      const apps = await feishuAppService.getFeishuAppsWithBitable();\n      setFeishuApps(apps);\n    } catch (error: any) {\n      console.error('加载飞书应用失败:', error);\n      message.error('加载飞书应用列表失败');\n    }\n  };\n\n  const handleBindFeishuApp = async (account: Account) => {\n    setFeishuAccount(account);\n    await loadFeishuApps();\n    setFeishuBindModalVisible(true);\n  };\n\n  const handleConfirmBindFeishuApp = async () => {\n    if (!feishuAccount || !selectedFeishuAppId) {\n      message.error('请选择飞书应用');\n      return;\n    }\n\n    try {\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n\n      const response = await api.post(`/feishu/bind-app/${feishuAccount.id}?feishu_app_id=${selectedFeishuAppId}`);\n\n      if (response.data.success) {\n        message.success('飞书应用绑定成功！');\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('绑定飞书应用失败');\n      }\n    } catch (error: any) {\n      console.error('绑定飞书应用失败:', error);\n      message.error(`绑定失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n\n  const handleSyncToFeishu = (account: Account) => {\n    if (!account.feishu_app_id) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n\n    // 显示确认对话框\n    Modal.confirm({\n      title: '确认同步数据到飞书',\n      content: (\n        <div>\n          <p>此操作将：</p>\n          <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>\n            <li>从服务器存储目录读取已下载的Excel文件</li>\n            <li>删除飞书表格中的已有数据</li>\n            <li>将新数据同步到飞书多维表格</li>\n          </ul>\n          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>\n            请确保已先下载数据到服务器！\n          </p>\n        </div>\n      ),\n      okText: '确认同步',\n      cancelText: '取消',\n      onOk: () => {\n        // 直接开始同步，不需要显示日期选择模态框\n        handleDirectSync(account);\n      }\n    });\n  };\n\n  const handleDirectSync = async (account: Account) => {\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(account.id));\n\n      const response = await api.post(`/feishu/sync-data/${account.id}`);\n\n      if (response.data.success) {\n        const { sync_results } = response.data;\n        message.success(\n          `数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`\n        );\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error: any) {\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(account.id);\n        return newSet;\n      });\n    }\n  };\n\n  const handleFeishuSyncSubmit = async (values: any) => {\n    if (!feishuAccount) return;\n\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n\n      const response = await api.post(`/feishu/sync-data/${feishuAccount.id}`, {\n        begin_date: values.begin_date,\n        end_date: values.end_date\n      });\n\n      if (response.data.success) {\n        const { sync_results, data_summary } = response.data;\n        message.success(\n          `数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`\n        );\n        setFeishuSyncModalVisible(false);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error: any) {\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n\n  const handleFeishuSyncModalClose = () => {\n    setFeishuSyncModalVisible(false);\n    setFeishuAccount(null);\n    feishuSyncForm.resetFields();\n  };\n\n  const columns = [\n    {\n      title: '账号名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '平台类型',\n      dataIndex: 'platform',\n      key: 'platform',\n      render: (platform: string) => {\n        const option = platformOptions.find(opt => opt.value === platform);\n        return option ? option.label : platform;\n      },\n    },\n    {\n      title: '登录状态',\n      dataIndex: 'login_status',\n      key: 'login_status',\n      render: (status: boolean) => (\n        <Tag color={status ? 'green' : 'red'}>\n          {status ? '已登录' : '未登录'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'last_login_time',\n      key: 'last_login_time',\n      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',\n    },\n    {\n      title: '飞书状态',\n      key: 'feishu_status',\n      render: (_: any, record: Account) => (\n        <Tag color={record.feishu_app_id ? 'blue' : 'default'}>\n          {record.feishu_app_id ? '已创建' : '未创建'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: Account) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"primary\" \n            icon={<LoginOutlined />} \n            size=\"small\"\n            onClick={() => handleLogin(record)}\n            disabled={record.login_status}\n          >\n            登录\n          </Button>\n          <Button\n            type=\"default\"\n            icon={<DownloadOutlined />}\n            size=\"small\"\n            onClick={() => handleDownload(record)}\n            disabled={!record.login_status || !record.feishu_app_id}\n          >\n            下载到服务器\n          </Button>\n          {!record.feishu_app_id ? (\n            <Button\n              type=\"default\"\n              icon={<LinkOutlined />}\n              size=\"small\"\n              onClick={() => handleBindFeishuApp(record)}\n              loading={feishuLoadingAccounts.has(record.id)}\n              disabled={feishuLoadingAccounts.has(record.id)}\n            >\n              绑定飞书应用\n            </Button>\n          ) : (\n            <Button\n              type=\"default\"\n              icon={<CloudSyncOutlined />}\n              size=\"small\"\n              onClick={() => handleSyncToFeishu(record)}\n              disabled={!record.login_status || feishuLoadingAccounts.has(record.id)}\n              loading={feishuLoadingAccounts.has(record.id)}\n            >\n              同步到飞书\n            </Button>\n          )}\n          <Popconfirm\n            title=\"确定要注销这个账号的登录状态吗？\"\n            description=\"注销后需要重新扫码登录\"\n            onConfirm={() => handleLogout(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"default\"\n              icon={<LogoutOutlined />} \n              size=\"small\"\n              loading={logoutLoading === record.id}\n              disabled={!record.login_status || logoutLoading === record.id}\n            >\n              注销\n            </Button>\n          </Popconfirm>\n          <Button \n            icon={<EditOutlined />} \n            size=\"small\"\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个账号吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              danger \n              icon={<DeleteOutlined />} \n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card \n        title=\"账号管理\" \n        extra={\n          <Space>\n            <Popconfirm\n              title=\"确定要注销所有账号的登录状态吗？\"\n              description=\"这将注销当前用户的所有微信公众号账号登录状态\"\n              onConfirm={handleLogoutAll}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button \n                type=\"default\"\n                icon={<LogoutOutlined />}\n                loading={loading}\n                disabled={accounts.length === 0 || !accounts.some(acc => acc.login_status)}\n              >\n                注销全部\n              </Button>\n            </Popconfirm>\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加账号\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={accounts}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAccount ? '编辑账号' : '添加账号'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"账号名称\"\n            rules={[{ required: true, message: '请输入账号名称' }]}\n          >\n            <Input placeholder=\"请输入账号名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"platform\"\n            label=\"平台类型\"\n            rules={[{ required: true, message: '请选择平台类型' }]}\n          >\n            <Select placeholder=\"请选择平台类型\">\n              {platformOptions.map(option => (\n                <Option key={option.value} value={option.value}>\n                  {option.label}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 登录模态框 */}\n      <Modal\n        title={`登录 ${loginAccount?.name}`}\n        open={loginModalVisible}\n        onCancel={handleLoginModalClose}\n        footer={[\n          <Button key=\"close\" onClick={handleLoginModalClose}>\n            关闭\n          </Button>,\n          <Button\n            key=\"retry\"\n            type=\"primary\"\n            onClick={() => loginAccount && handleLogin(loginAccount)}\n            disabled={loginLoading || loginStatus === 'success'}\n          >\n            重新获取二维码\n          </Button>\n        ]}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          {loginLoading && (\n            <div>\n              <Spin size=\"large\" />\n              <p style={{ marginTop: 16 }}>正在获取二维码...</p>\n            </div>\n          )}\n\n          {loginStatus === 'scanning' && qrCodeUrl && (\n            <div>\n              <Image\n                src={qrCodeUrl}\n                alt=\"登录二维码\"\n                width={200}\n                height={200}\n                style={{ border: '1px solid #d9d9d9' }}\n              />\n              <p style={{ marginTop: 16, color: '#1890ff' }}>\n                <QrcodeOutlined /> 请使用微信扫描二维码登录\n              </p>\n              <p style={{ color: '#666', fontSize: '12px' }}>\n                二维码将在30秒后过期\n              </p>\n            </div>\n          )}\n\n          {loginStatus === 'success' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>\n                ✓\n              </div>\n              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>\n            </div>\n          )}\n\n          {loginStatus === 'failed' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>\n                ✗\n              </div>\n              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>\n            </div>\n          )}\n\n          {loginStatus === 'waiting' && !loginLoading && (\n            <div>\n              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />\n              <p style={{ color: '#666' }}>准备获取登录二维码...</p>\n            </div>\n          )}\n        </div>\n      </Modal>\n\n      {/* 下载数据模态框 */}\n      <Modal\n        title={`下载数据 - ${downloadAccount?.name}`}\n        open={downloadModalVisible}\n        onCancel={handleDownloadModalClose}\n        onOk={() => downloadForm.submit()}\n        okText=\"下载\"\n        cancelText=\"取消\"\n        confirmLoading={downloadLoading}\n        width={500}\n      >\n        <Form\n          form={downloadForm}\n          layout=\"vertical\"\n          onFinish={handleDownloadSubmit}\n        >\n          <Form.Item\n            name=\"start_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"data_types\"\n            label=\"选择数据类型\"\n            rules={[{ required: true, message: '请至少选择一种数据类型' }]}\n          >\n            <Checkbox.Group style={{ width: '100%' }}>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n                <Checkbox value=\"content_trend\">内容数据趋势明细表</Checkbox>\n                <Checkbox value=\"content_source\">内容流量来源明细表</Checkbox>\n                <Checkbox value=\"content_detail\">内容已通知内容明细表</Checkbox>\n                <Checkbox value=\"user_channel\">用户渠道构成明细表</Checkbox>\n              </div>\n            </Checkbox.Group>\n          </Form.Item>\n\n          {downloadProgress && (\n            <div style={{ background: '#e6f7ff', padding: '12px', borderRadius: '6px', marginBottom: '16px' }}>\n              <p style={{ margin: 0, fontSize: '14px', color: '#1890ff' }}>\n                {downloadProgress}\n              </p>\n            </div>\n          )}\n          \n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>可选择多种数据类型进行批量下载到服务器</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>文件将保存在服务器存储目录中，不会下载到本地</li>\n              <li>采用异步任务模式，支持实时进度监控</li>\n              <li>多个文件下载时会自动间隔2-5秒</li>\n              <li>请确保账号已登录且已绑定飞书应用</li>\n              <li>下载完成后可使用\"同步到飞书\"功能</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n\n      {/* 飞书数据同步模态框 */}\n      <Modal\n        title={`同步数据到飞书 - ${feishuAccount?.name}`}\n        open={feishuSyncModalVisible}\n        onCancel={handleFeishuSyncModalClose}\n        onOk={() => feishuSyncForm.submit()}\n        okText=\"开始同步\"\n        cancelText=\"取消\"\n        confirmLoading={feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false}\n        width={500}\n      >\n        <Form\n          form={feishuSyncForm}\n          layout=\"vertical\"\n          onFinish={handleFeishuSyncSubmit}\n        >\n          <Form.Item\n            name=\"begin_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n\n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>将下载微信公众号数据并自动同步到飞书多维表格</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>会自动创建用户概况和图文分析两个数据表</li>\n              <li>请确保账号已登录且已创建飞书表格</li>\n              <li>同步时间可能较长，请耐心等待</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n\n      {/* 绑定飞书应用模态框 */}\n      <Modal\n        title={`绑定飞书应用 - ${feishuAccount?.name}`}\n        open={feishuBindModalVisible}\n        onCancel={() => {\n          setFeishuBindModalVisible(false);\n          setFeishuAccount(null);\n          setSelectedFeishuAppId(null);\n        }}\n        onOk={handleConfirmBindFeishuApp}\n        okText=\"确认绑定\"\n        cancelText=\"取消\"\n        confirmLoading={feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false}\n        width={500}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <p>请选择要绑定的飞书应用：</p>\n          <Select\n            style={{ width: '100%' }}\n            placeholder=\"请选择飞书应用\"\n            value={selectedFeishuAppId}\n            onChange={setSelectedFeishuAppId}\n            showSearch\n            optionFilterProp=\"children\"\n          >\n            {feishuApps.map(app => (\n              <Option key={app.id} value={app.id}>\n                {app.name} - {app.bitable_name}\n              </Option>\n            ))}\n          </Select>\n        </div>\n\n        <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px' }}>\n          <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n            <strong>说明：</strong>\n          </p>\n          <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n            <li>只显示已创建多维表格的飞书应用</li>\n            <li>绑定后将删除账号原有的数据表，并创建新的数据表</li>\n            <li>如需创建新的飞书应用，请前往\"飞书应用管理\"页面</li>\n          </ul>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AccountManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,mBAAmB;AAChL,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAASC,gBAAgB,QAAmB,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAM;EAAEC;AAAO,CAAC,GAAGrB,MAAM;AAczB,MAAMsB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACwC,IAAI,CAAC,GAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAiB,IAAI,CAAC;EACtE,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAS,SAAS,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACoD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC4D,YAAY,CAAC,GAAGtD,IAAI,CAACmC,OAAO,CAAC,CAAC;;EAErC;EACA,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAAC+D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAiB,IAAI,CAAC;EACxE,MAAM,CAACmE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpE,QAAQ,CAAc,IAAIqE,GAAG,CAAC,CAAC,CAAC;EAC1F,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwE,cAAc,CAAC,GAAGlE,IAAI,CAACmC,OAAO,CAAC,CAAC;EACvC,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAgB,IAAI,CAAC;EAEnF,MAAM6E,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAM,CAAC,CACvC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC7C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMxD,GAAG,CAACyD,GAAG,CAAC,YAAY,CAAC;MAC5CjD,WAAW,CAACgD,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3E,OAAO,CAAC2E,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACd+E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB9C,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAAC8C,WAAW,CAAC,CAAC;IAClBjD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkD,UAAU,GAAIC,OAAgB,IAAK;IACvCjD,iBAAiB,CAACiD,OAAO,CAAC;IAC1BhD,IAAI,CAACiD,cAAc,CAACD,OAAO,CAAC;IAC5BnD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqD,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMlE,GAAG,CAACmE,MAAM,CAAC,aAAaD,EAAE,EAAE,CAAC;MACnClF,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvBb,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd3E,OAAO,CAAC2E,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAIzD,cAAc,EAAE;QAClB,MAAMb,GAAG,CAACuE,GAAG,CAAC,aAAa1D,cAAc,CAACqD,EAAE,EAAE,EAAEI,MAAM,CAAC;QACvDtF,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMpE,GAAG,CAACwE,IAAI,CAAC,YAAY,EAAEF,MAAM,CAAC;QACpCtF,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACzB;MACAxD,eAAe,CAAC,KAAK,CAAC;MACtB2C,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnB1F,OAAO,CAAC2E,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOb,OAAgB,IAAK;IAC9C3C,eAAe,CAAC2C,OAAO,CAAC;IACxB7C,oBAAoB,CAAC,IAAI,CAAC;IAC1BQ,cAAc,CAAC,SAAS,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMgC,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MACrE,IAAIV,QAAQ,CAACE,IAAI,CAACmB,MAAM,EAAE;QACxBvD,YAAY,CAACkC,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC;QAClCnD,cAAc,CAAC,UAAU,CAAC;;QAE1B;QACAoD,uBAAuB,CAACf,OAAO,CAACG,EAAE,CAAC;MACrC,CAAC,MAAM;QACLlF,OAAO,CAAC2E,KAAK,CAAC,SAAS,CAAC;QACxBjC,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBhG,OAAO,CAAC2E,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,SAAS,CAAC;MACxDjD,cAAc,CAAC,QAAQ,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsD,uBAAuB,GAAIG,SAAiB,IAAK;IACrD,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMxD,GAAG,CAACyD,GAAG,CAAC,wBAAwBwB,SAAS,EAAE,CAAC;QACnE,MAAM;UAAEG;QAAU,CAAC,GAAG5B,QAAQ,CAACE,IAAI;QAEnC,IAAI0B,SAAS,EAAE;UACb1D,cAAc,CAAC,SAAS,CAAC;UACzB1C,OAAO,CAACoF,OAAO,CAAC,OAAO,CAAC;UACxBiB,aAAa,CAACH,YAAY,CAAC;UAC3BhE,oBAAoB,CAAC,KAAK,CAAC;UAC3BqC,aAAa,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA4B,UAAU,CAAC,MAAM;MACfF,aAAa,CAACH,YAAY,CAAC;MAC3B,IAAIzD,WAAW,KAAK,UAAU,EAAE;QAC9BC,cAAc,CAAC,QAAQ,CAAC;QACxB1C,OAAO,CAACwG,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClCvE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMgE,cAAc,GAAI3B,OAAgB,IAAK;IAC3C,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;MACzB3G,OAAO,CAACwG,OAAO,CAAC,cAAc,CAAC;MAC/B;IACF;IACA,IAAI,CAACzB,OAAO,CAAC6B,aAAa,EAAE;MAC1B5G,OAAO,CAACwG,OAAO,CAAC,gBAAgB,CAAC;MACjC;IACF;IACA1D,kBAAkB,CAACiC,OAAO,CAAC;;IAE3B;IACA,MAAM8B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAC/BE,OAAO,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEtC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACC,OAAO,CAAC;IACnCG,SAAS,CAACF,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;IAE3C9D,YAAY,CAAC6B,cAAc,CAAC;MAC1BmC,UAAU,EAAED,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAEP,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CE,UAAU,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACpF,CAAC,CAAC;IAEF3E,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAM4E,oBAAoB,GAAG,MAAOlC,MAAW,IAAK;IAClD,IAAI,CAACzC,eAAe,EAAE;IAEtB,MAAM4E,aAAa,GAAGnC,MAAM,CAACiC,UAAU;IACvC,IAAI,CAACE,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MAChD1H,OAAO,CAAC2E,KAAK,CAAC,aAAa,CAAC;MAC5B;IACF;IAEA3B,kBAAkB,CAAC,IAAI,CAAC;IACxBE,mBAAmB,CAAC,eAAe,CAAC;IAEpC,IAAI;MACF;MACA,MAAMsB,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,+BAA+B3C,eAAe,CAACqC,EAAE,EAAE,EAAE;QACnFiC,UAAU,EAAE7B,MAAM,CAAC6B,UAAU;QAC7BG,QAAQ,EAAEhC,MAAM,CAACgC,QAAQ;QACzBC,UAAU,EAAEE;MACd,CAAC,CAAC;MAEF,IAAIjD,QAAQ,CAACE,IAAI,CAACU,OAAO,IAAIZ,QAAQ,CAACE,IAAI,CAACiD,OAAO,EAAE;QAClD,MAAMC,MAAM,GAAGpD,QAAQ,CAACE,IAAI,CAACiD,OAAO;QACpCzE,mBAAmB,CAAC,mBAAmB,CAAC;;QAExC;QACA,MAAMgD,YAAY,GAAGC,WAAW,CAAC,YAAY;UAC3C,IAAI;YACF,MAAM0B,cAAc,GAAG,MAAM7G,GAAG,CAACyD,GAAG,CAAC,gCAAgCmD,MAAM,EAAE,CAAC;YAC9E,MAAME,UAAU,GAAGD,cAAc,CAACnD,IAAI,CAACqD,WAAW;YAElD,IAAID,UAAU,CAACE,MAAM,KAAK,SAAS,EAAE;cACnC9E,mBAAmB,CACjB,QAAQ4E,UAAU,CAACG,QAAQ,IAAIH,UAAU,CAACI,KAAK,SAASJ,UAAU,CAACK,YAAY,IAAI,KAAK,GAC1F,CAAC;YACH,CAAC,MAAM,IAAIL,UAAU,CAACE,MAAM,KAAK,WAAW,EAAE;cAC5C3B,aAAa,CAACH,YAAY,CAAC;cAC3B,MAAM;gBAAEkC,gBAAgB;gBAAEC;cAAa,CAAC,GAAGP,UAAU;cAErD,IAAIM,gBAAgB,CAACV,MAAM,GAAG,CAAC,EAAE;gBAC/B1H,OAAO,CAACoF,OAAO,CAAC,cAAcgD,gBAAgB,CAACV,MAAM,OAAOW,YAAY,CAACX,MAAM,GAAG,CAAC,GAAG,QAAQW,YAAY,CAACX,MAAM,MAAM,GAAG,EAAE,EAAE,CAAC;cACjI;cAEA,IAAIW,YAAY,CAACX,MAAM,KAAK,CAAC,EAAE;gBAC7B9E,uBAAuB,CAAC,KAAK,CAAC;cAChC,CAAC,MAAM;gBACL0D,OAAO,CAAC3B,KAAK,CAAC,UAAU,EAAE0D,YAAY,CAAC;cACzC;cAEArF,kBAAkB,CAAC,KAAK,CAAC;cACzBE,mBAAmB,CAAC,EAAE,CAAC;YACzB,CAAC,MAAM,IAAI4E,UAAU,CAACE,MAAM,KAAK,QAAQ,EAAE;cACzC3B,aAAa,CAACH,YAAY,CAAC;cAC3BlG,OAAO,CAAC2E,KAAK,CAAC,WAAWmD,UAAU,CAAC9H,OAAO,EAAE,CAAC;cAC9CgD,kBAAkB,CAAC,KAAK,CAAC;cACzBE,mBAAmB,CAAC,EAAE,CAAC;YACzB;UACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;YACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACnC;QACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEV;QACA4B,UAAU,CAAC,MAAM;UACfF,aAAa,CAACH,YAAY,CAAC;UAC3B,IAAInD,eAAe,EAAE;YACnB/C,OAAO,CAACwG,OAAO,CAAC,kBAAkB,CAAC;YACnCxD,kBAAkB,CAAC,KAAK,CAAC;YACzBE,mBAAmB,CAAC,EAAE,CAAC;UACzB;QACF,CAAC,EAAE,MAAM,CAAC;MAEZ,CAAC,MAAM;QACLlD,OAAO,CAAC2E,KAAK,CAAC,UAAU,CAAC;QACzB3B,kBAAkB,CAAC,KAAK,CAAC;QACzBE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOyB,KAAU,EAAE;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MACnBjC,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3E,OAAO,CAAC2E,KAAK,CAAC,aAAa,EAAA2D,gBAAA,GAAA3D,KAAK,CAACH,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsB5C,MAAM,KAAIhB,KAAK,CAAC3E,OAAO,EAAE,CAAC;MAC3EgD,kBAAkB,CAAC,KAAK,CAAC;MACzBE,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMsF,wBAAwB,GAAGA,CAAA,KAAM;IACrC5F,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;IACxBI,mBAAmB,CAAC,EAAE,CAAC;IACvBC,YAAY,CAAC0B,WAAW,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAG,MAAO1D,OAAgB,IAAK;IAC/C,IAAI;MACF1B,gBAAgB,CAAC0B,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,kBAAkBT,OAAO,CAACG,EAAE,EAAE,EAAE;QAC9DwD,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIlE,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBpF,OAAO,CAACoF,OAAO,CAAC,MAAML,OAAO,CAAC4D,IAAI,OAAO,CAAC;QAC1C;QACApE,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLvE,OAAO,CAACwG,OAAO,CAAC,MAAMzB,OAAO,CAAC4D,IAAI,iBAAiB,CAAC;QACpD;QACApE,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAiE,gBAAA,EAAAC,qBAAA;MACnBvC,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B3E,OAAO,CAAC2E,KAAK,CAAC,SAAS,EAAAiE,gBAAA,GAAAjE,KAAK,CAACH,QAAQ,cAAAoE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlE,IAAI,cAAAmE,qBAAA,uBAApBA,qBAAA,CAAsBlD,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACRtC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyF,iBAAiB,GAAG,MAAO/D,OAAgB,IAAK;IACpD,IAAI;MACF1B,gBAAgB,CAAC0B,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MAErE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBpF,OAAO,CAACoF,OAAO,CAAC,MAAML,OAAO,CAAC4D,IAAI,SAAS,CAAC;MAC9C,CAAC,MAAM;QACL3I,OAAO,CAACwG,OAAO,CAAC,MAAMzB,OAAO,CAAC4D,IAAI,SAAS,CAAC;MAC9C;;MAEA;MACApE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAoE,gBAAA,EAAAC,qBAAA;MACnB1C,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3E,OAAO,CAAC2E,KAAK,CAAC,WAAW,EAAAoE,gBAAA,GAAApE,KAAK,CAACH,QAAQ,cAAAuE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrE,IAAI,cAAAsE,qBAAA,uBAApBA,qBAAA,CAAsBrD,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRtC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM4F,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFvH,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM8C,QAAQ,GAAG,MAAMxD,GAAG,CAACyD,GAAG,CAAC,oBAAoB,CAAC;MAEpD,MAAM;QAAEW,OAAO;QAAEpF,OAAO,EAAEkJ,GAAG;QAAEC;MAAe,CAAC,GAAG3E,QAAQ,CAACE,IAAI;MAE/D,IAAIU,OAAO,EAAE;QACXpF,OAAO,CAACoF,OAAO,CAAC8D,GAAG,CAAC;MACtB,CAAC,MAAM;QACLlJ,OAAO,CAACwG,OAAO,CAAC0C,GAAG,CAAC;MACtB;;MAEA;MACA,IAAIC,cAAc,IAAIA,cAAc,CAACzB,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAM0B,YAAY,GAAGD,cAAc,CAACE,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAClE,OAAO,CAAC,CAACsC,MAAM;QACxE,MAAM6B,UAAU,GAAGJ,cAAc,CAACzB,MAAM;QAExC,IAAI0B,YAAY,KAAKG,UAAU,EAAE;UAC/BvJ,OAAO,CAACoF,OAAO,CAAC,MAAMmE,UAAU,UAAU,CAAC;QAC7C,CAAC,MAAM;UACLvJ,OAAO,CAACwG,OAAO,CAAC,GAAG4C,YAAY,IAAIG,UAAU,UAAU,CAAC;QAC1D;MACF;;MAEA;MACAhF,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAA6E,gBAAA,EAAAC,qBAAA;MACnBnD,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3E,OAAO,CAAC2E,KAAK,CAAC,WAAW,EAAA6E,gBAAA,GAAA7E,KAAK,CAACH,QAAQ,cAAAgF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9E,IAAI,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsB9D,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRjE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgI,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAM1I,gBAAgB,CAAC2I,wBAAwB,CAAC,CAAC;MAC9D3F,aAAa,CAAC0F,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOhF,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3E,OAAO,CAAC2E,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,MAAMkF,mBAAmB,GAAG,MAAO9E,OAAgB,IAAK;IACtDtB,gBAAgB,CAACsB,OAAO,CAAC;IACzB,MAAM2E,cAAc,CAAC,CAAC;IACtBnG,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMuG,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAACtG,aAAa,IAAI,CAACU,mBAAmB,EAAE;MAC1ClE,OAAO,CAAC2E,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI;MACFhB,wBAAwB,CAACoG,IAAI,IAAI,IAAInG,GAAG,CAACmG,IAAI,CAAC,CAACC,GAAG,CAACxG,aAAa,CAAC0B,EAAE,CAAC,CAAC;MAErE,MAAMV,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,oBAAoBhC,aAAa,CAAC0B,EAAE,kBAAkBhB,mBAAmB,EAAE,CAAC;MAE5G,IAAIM,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBpF,OAAO,CAACoF,OAAO,CAAC,WAAW,CAAC;QAC5B7B,yBAAyB,CAAC,KAAK,CAAC;QAChCE,gBAAgB,CAAC,IAAI,CAAC;QACtBU,sBAAsB,CAAC,IAAI,CAAC;QAC5BI,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLvE,OAAO,CAAC2E,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAsF,gBAAA,EAAAC,qBAAA;MACnB5D,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3E,OAAO,CAAC2E,KAAK,CAAC,SAAS,EAAAsF,gBAAA,GAAAtF,KAAK,CAACH,QAAQ,cAAAyF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvF,IAAI,cAAAwF,qBAAA,uBAApBA,qBAAA,CAAsBvE,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACRhC,wBAAwB,CAACoG,IAAI,IAAI;QAC/B,MAAMI,MAAM,GAAG,IAAIvG,GAAG,CAACmG,IAAI,CAAC;QAC5BI,MAAM,CAAChF,MAAM,CAAC3B,aAAa,CAAC0B,EAAE,CAAC;QAC/B,OAAOiF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIrF,OAAgB,IAAK;IAC/C,IAAI,CAACA,OAAO,CAAC6B,aAAa,EAAE;MAC1B5G,OAAO,CAACwG,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACA,IAAI,CAACzB,OAAO,CAAC4B,YAAY,EAAE;MACzB3G,OAAO,CAACwG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;;IAEA;IACA5G,KAAK,CAACyK,OAAO,CAAC;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,eACLpJ,OAAA;QAAAqJ,QAAA,gBACErJ,OAAA;UAAAqJ,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACZzJ,OAAA;UAAI0J,KAAK,EAAE;YAAEC,WAAW,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACnDrJ,OAAA;YAAAqJ,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BzJ,OAAA;YAAAqJ,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBzJ,OAAA;YAAAqJ,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACLzJ,OAAA;UAAG0J,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDM,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACV;QACAC,gBAAgB,CAACtG,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsG,gBAAgB,GAAG,MAAOtG,OAAgB,IAAK;IACnD,IAAI;MACF;MACApB,wBAAwB,CAACoG,IAAI,IAAI,IAAInG,GAAG,CAACmG,IAAI,CAAC,CAACC,GAAG,CAACjF,OAAO,CAACG,EAAE,CAAC,CAAC;MAE/D,MAAMV,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,qBAAqBT,OAAO,CAACG,EAAE,EAAE,CAAC;MAElE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB,MAAM;UAAEkG;QAAa,CAAC,GAAG9G,QAAQ,CAACE,IAAI;QACtC1E,OAAO,CAACoF,OAAO,CACb,gBAAgBkG,YAAY,CAACC,gBAAgB,YAAYD,YAAY,CAACE,mBAAmB,IAC3F,CAAC;MACH,CAAC,MAAM;QACLxL,OAAO,CAAC2E,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAA8G,gBAAA,EAAAC,qBAAA;MACnBpF,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3E,OAAO,CAAC2E,KAAK,CAAC,SAAS,EAAA8G,gBAAA,GAAA9G,KAAK,CAACH,QAAQ,cAAAiH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/G,IAAI,cAAAgH,qBAAA,uBAApBA,qBAAA,CAAsB/F,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR;MACAhC,wBAAwB,CAACoG,IAAI,IAAI;QAC/B,MAAMI,MAAM,GAAG,IAAIvG,GAAG,CAACmG,IAAI,CAAC;QAC5BI,MAAM,CAAChF,MAAM,CAACJ,OAAO,CAACG,EAAE,CAAC;QACzB,OAAOiF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMwB,sBAAsB,GAAG,MAAOrG,MAAW,IAAK;IACpD,IAAI,CAAC9B,aAAa,EAAE;IAEpB,IAAI;MACF;MACAG,wBAAwB,CAACoG,IAAI,IAAI,IAAInG,GAAG,CAACmG,IAAI,CAAC,CAACC,GAAG,CAACxG,aAAa,CAAC0B,EAAE,CAAC,CAAC;MAErE,MAAMV,QAAQ,GAAG,MAAMxD,GAAG,CAACwE,IAAI,CAAC,qBAAqBhC,aAAa,CAAC0B,EAAE,EAAE,EAAE;QACvE0G,UAAU,EAAEtG,MAAM,CAACsG,UAAU;QAC7BtE,QAAQ,EAAEhC,MAAM,CAACgC;MACnB,CAAC,CAAC;MAEF,IAAI9C,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB,MAAM;UAAEkG,YAAY;UAAEO;QAAa,CAAC,GAAGrH,QAAQ,CAACE,IAAI;QACpD1E,OAAO,CAACoF,OAAO,CACb,gBAAgBkG,YAAY,CAACC,gBAAgB,YAAYD,YAAY,CAACE,mBAAmB,IAC3F,CAAC;QACD1H,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,MAAM;QACL9D,OAAO,CAAC2E,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAmH,gBAAA,EAAAC,qBAAA;MACnBzF,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3E,OAAO,CAAC2E,KAAK,CAAC,SAAS,EAAAmH,gBAAA,GAAAnH,KAAK,CAACH,QAAQ,cAAAsH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpH,IAAI,cAAAqH,qBAAA,uBAApBA,qBAAA,CAAsBpG,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR;MACAhC,wBAAwB,CAACoG,IAAI,IAAI;QAC/B,MAAMI,MAAM,GAAG,IAAIvG,GAAG,CAACmG,IAAI,CAAC;QAC5BI,MAAM,CAAChF,MAAM,CAAC3B,aAAa,CAAC0B,EAAE,CAAC;QAC/B,OAAOiF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6B,0BAA0B,GAAGA,CAAA,KAAM;IACvClI,yBAAyB,CAAC,KAAK,CAAC;IAChCL,gBAAgB,CAAC,IAAI,CAAC;IACtBM,cAAc,CAACc,WAAW,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMoH,OAAO,GAAG,CACd;IACE3B,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACE7B,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,QAAgB,IAAK;MAC5B,MAAMC,MAAM,GAAGlI,eAAe,CAACmI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnI,KAAK,KAAKgI,QAAQ,CAAC;MAClE,OAAOC,MAAM,GAAGA,MAAM,CAAChI,KAAK,GAAG+H,QAAQ;IACzC;EACF,CAAC,EACD;IACE/B,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGpE,MAAe,iBACtB7G,OAAA,CAACjB,GAAG;MAAC8K,KAAK,EAAEhD,MAAM,GAAG,OAAO,GAAG,KAAM;MAAAwC,QAAA,EAClCxC,MAAM,GAAG,KAAK,GAAG;IAAK;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACf4B,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAGK,IAAmB,IAAKA,IAAI,GAAG,IAAI3F,IAAI,CAAC2F,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC5E,CAAC,EACD;IACEpC,KAAK,EAAE,MAAM;IACb6B,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACO,CAAM,EAAEC,MAAe,kBAC9BzL,OAAA,CAACjB,GAAG;MAAC8K,KAAK,EAAE4B,MAAM,CAAChG,aAAa,GAAG,MAAM,GAAG,SAAU;MAAA4D,QAAA,EACnDoC,MAAM,CAAChG,aAAa,GAAG,KAAK,GAAG;IAAK;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAET,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGK,IAAY,IAAK,IAAI3F,IAAI,CAAC2F,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACX6B,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACO,CAAM,EAAEC,MAAe,kBAC9BzL,OAAA,CAAClB,KAAK;MAAC4M,IAAI,EAAC,QAAQ;MAAArC,QAAA,gBAClBrJ,OAAA,CAACxB,MAAM;QACLmN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE5L,OAAA,CAACT,aAAa;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBiC,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMpH,WAAW,CAACgH,MAAM,CAAE;QACnCK,QAAQ,EAAEL,MAAM,CAACjG,YAAa;QAAA6D,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzJ,OAAA,CAACxB,MAAM;QACLmN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE5L,OAAA,CAACP,gBAAgB;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BiC,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMtG,cAAc,CAACkG,MAAM,CAAE;QACtCK,QAAQ,EAAE,CAACL,MAAM,CAACjG,YAAY,IAAI,CAACiG,MAAM,CAAChG,aAAc;QAAA4D,QAAA,EACzD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAACgC,MAAM,CAAChG,aAAa,gBACpBzF,OAAA,CAACxB,MAAM;QACLmN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE5L,OAAA,CAACJ,YAAY;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBiC,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMnD,mBAAmB,CAAC+C,MAAM,CAAE;QAC3CnL,OAAO,EAAEiC,qBAAqB,CAACwJ,GAAG,CAACN,MAAM,CAAC1H,EAAE,CAAE;QAC9C+H,QAAQ,EAAEvJ,qBAAqB,CAACwJ,GAAG,CAACN,MAAM,CAAC1H,EAAE,CAAE;QAAAsF,QAAA,EAChD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETzJ,OAAA,CAACxB,MAAM;QACLmN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE5L,OAAA,CAACL,iBAAiB;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BiC,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAACwC,MAAM,CAAE;QAC1CK,QAAQ,EAAE,CAACL,MAAM,CAACjG,YAAY,IAAIjD,qBAAqB,CAACwJ,GAAG,CAACN,MAAM,CAAC1H,EAAE,CAAE;QACvEzD,OAAO,EAAEiC,qBAAqB,CAACwJ,GAAG,CAACN,MAAM,CAAC1H,EAAE,CAAE;QAAAsF,QAAA,EAC/C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACDzJ,OAAA,CAAChB,UAAU;QACTmK,KAAK,EAAC,kGAAkB;QACxB6C,WAAW,EAAC,oEAAa;QACzBC,SAAS,EAAEA,CAAA,KAAM3E,YAAY,CAACmE,MAAM,CAAE;QACtC1B,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEfrJ,OAAA,CAACxB,MAAM;UACLmN,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE5L,OAAA,CAACN,cAAc;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBiC,IAAI,EAAC,OAAO;UACZpL,OAAO,EAAE2B,aAAa,KAAKwJ,MAAM,CAAC1H,EAAG;UACrC+H,QAAQ,EAAE,CAACL,MAAM,CAACjG,YAAY,IAAIvD,aAAa,KAAKwJ,MAAM,CAAC1H,EAAG;UAAAsF,QAAA,EAC/D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACbzJ,OAAA,CAACxB,MAAM;QACLoN,IAAI,eAAE5L,OAAA,CAACX,YAAY;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBiC,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMlI,UAAU,CAAC8H,MAAM,CAAE;QAAApC,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzJ,OAAA,CAAChB,UAAU;QACTmK,KAAK,EAAC,oEAAa;QACnB8C,SAAS,EAAEA,CAAA,KAAMnI,YAAY,CAAC2H,MAAM,CAAC1H,EAAE,CAAE;QACzCgG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEfrJ,OAAA,CAACxB,MAAM;UACL0N,MAAM;UACNN,IAAI,eAAE5L,OAAA,CAACV,cAAc;YAAAgK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBiC,IAAI,EAAC,OAAO;UAAArC,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEzJ,OAAA;IAAAqJ,QAAA,gBACErJ,OAAA,CAAC1B,IAAI;MACH6K,KAAK,EAAC,0BAAM;MACZgD,KAAK,eACHnM,OAAA,CAAClB,KAAK;QAAAuK,QAAA,gBACJrJ,OAAA,CAAChB,UAAU;UACTmK,KAAK,EAAC,kGAAkB;UACxB6C,WAAW,EAAC,sIAAwB;UACpCC,SAAS,EAAEnE,eAAgB;UAC3BiC,MAAM,EAAC,cAAI;UACXC,UAAU,EAAC,cAAI;UAAAX,QAAA,eAEfrJ,OAAA,CAACxB,MAAM;YACLmN,IAAI,EAAC,SAAS;YACdC,IAAI,eAAE5L,OAAA,CAACN,cAAc;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBnJ,OAAO,EAAEA,OAAQ;YACjBwL,QAAQ,EAAE1L,QAAQ,CAACmG,MAAM,KAAK,CAAC,IAAI,CAACnG,QAAQ,CAACgM,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7G,YAAY,CAAE;YAAA6D,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACbzJ,OAAA,CAACxB,MAAM;UACLmN,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE5L,OAAA,CAACZ,YAAY;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoC,OAAO,EAAEpI,SAAU;UAAA4F,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAEDrJ,OAAA,CAACzB,KAAK;QACJuM,OAAO,EAAEA,OAAQ;QACjBwB,UAAU,EAAElM,QAAS;QACrBmM,MAAM,EAAC,IAAI;QACXjM,OAAO,EAAEA,OAAQ;QACjBkM,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAG5F,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPzJ,OAAA,CAACvB,KAAK;MACJ0K,KAAK,EAAEzI,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCkM,IAAI,EAAEpM,YAAa;MACnBqM,QAAQ,EAAEA,CAAA,KAAMpM,eAAe,CAAC,KAAK,CAAE;MACvCwJ,IAAI,EAAEA,CAAA,KAAMrJ,IAAI,CAACkM,MAAM,CAAC,CAAE;MAC1B/C,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAX,QAAA,eAEfrJ,OAAA,CAACtB,IAAI;QACHkC,IAAI,EAAEA,IAAK;QACXmM,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE9I,YAAa;QAAAmF,QAAA,gBAEvBrJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,MAAM;UACXrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACrB,KAAK;YAACyO,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZzJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,UAAU;UACfrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACpB,MAAM;YAACwO,WAAW,EAAC,4CAAS;YAAA/D,QAAA,EAC1BpG,eAAe,CAACoK,GAAG,CAAClC,MAAM,iBACzBnL,OAAA,CAACC,MAAM;cAAoBiD,KAAK,EAAEiI,MAAM,CAACjI,KAAM;cAAAmG,QAAA,EAC5C8B,MAAM,CAAChI;YAAK,GADFgI,MAAM,CAACjI,KAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRzJ,OAAA,CAACvB,KAAK;MACJ0K,KAAK,EAAE,MAAMnI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwG,IAAI,EAAG;MAClCoF,IAAI,EAAE9L,iBAAkB;MACxB+L,QAAQ,EAAEvH,qBAAsB;MAChCgI,MAAM,EAAE,cACNtN,OAAA,CAACxB,MAAM;QAAaqN,OAAO,EAAEvG,qBAAsB;QAAA+D,QAAA,EAAC;MAEpD,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTzJ,OAAA,CAACxB,MAAM;QAELmN,IAAI,EAAC,SAAS;QACdE,OAAO,EAAEA,CAAA,KAAM7K,YAAY,IAAIyD,WAAW,CAACzD,YAAY,CAAE;QACzD8K,QAAQ,EAAE1K,YAAY,IAAIE,WAAW,KAAK,SAAU;QAAA+H,QAAA,EACrD;MAED,GANM,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CAAC,CACT;MACF8D,KAAK,EAAE,GAAI;MAAAlE,QAAA,eAEXrJ,OAAA;QAAK0J,KAAK,EAAE;UAAE8D,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAApE,QAAA,GACpDjI,YAAY,iBACXpB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA,CAACd,IAAI;YAACwM,IAAI,EAAC;UAAO;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEgE,SAAS,EAAE;YAAG,CAAE;YAAArE,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,EAEAnI,WAAW,KAAK,UAAU,IAAIJ,SAAS,iBACtClB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA,CAACf,KAAK;YACJ0O,GAAG,EAAEzM,SAAU;YACf0M,GAAG,EAAC,gCAAO;YACXL,KAAK,EAAE,GAAI;YACXM,MAAM,EAAE,GAAI;YACZnE,KAAK,EAAE;cAAEoE,MAAM,EAAE;YAAoB;UAAE;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACFzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEgE,SAAS,EAAE,EAAE;cAAE7D,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,gBAC5CrJ,OAAA,CAACR,cAAc;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEkE,QAAQ,EAAE;YAAO,CAAE;YAAA1E,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAnI,WAAW,KAAK,SAAS,iBACxBtB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAK0J,KAAK,EAAE;cAAEqE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE,SAAS;cAAEmE,YAAY,EAAE;YAAG,CAAE;YAAA3E,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEG,KAAK,EAAE,SAAS;cAAEkE,QAAQ,EAAE;YAAO,CAAE;YAAA1E,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAnI,WAAW,KAAK,QAAQ,iBACvBtB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAK0J,KAAK,EAAE;cAAEqE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE,SAAS;cAAEmE,YAAY,EAAE;YAAG,CAAE;YAAA3E,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEG,KAAK,EAAE,SAAS;cAAEkE,QAAQ,EAAE;YAAO,CAAE;YAAA1E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEAnI,WAAW,KAAK,SAAS,IAAI,CAACF,YAAY,iBACzCpB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA,CAACR,cAAc;YAACkK,KAAK,EAAE;cAAEqE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE,SAAS;cAAEmE,YAAY,EAAE;YAAG;UAAE;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFzJ,OAAA;YAAG0J,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRzJ,OAAA,CAACvB,KAAK;MACJ0K,KAAK,EAAE,UAAUzH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8F,IAAI,EAAG;MACzCoF,IAAI,EAAEpL,oBAAqB;MAC3BqL,QAAQ,EAAExF,wBAAyB;MACnC4C,IAAI,EAAEA,CAAA,KAAMjI,YAAY,CAAC8K,MAAM,CAAC,CAAE;MAClC/C,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfiE,cAAc,EAAErM,eAAgB;MAChC2L,KAAK,EAAE,GAAI;MAAAlE,QAAA,eAEXrJ,OAAA,CAACtB,IAAI;QACHkC,IAAI,EAAEoB,YAAa;QACnB+K,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE3G,oBAAqB;QAAAgD,QAAA,gBAE/BrJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,YAAY;UACjBrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACrB,KAAK;YAACgN,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZzJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,UAAU;UACfrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACrB,KAAK;YAACgN,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZzJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,YAAY;UACjBrE,KAAK,EAAC,sCAAQ;UACd+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAc,CAAC,CAAE;UAAAwK,QAAA,eAEpDrJ,OAAA,CAACb,QAAQ,CAAC+O,KAAK;YAACxE,KAAK,EAAE;cAAE6D,KAAK,EAAE;YAAO,CAAE;YAAAlE,QAAA,eACvCrJ,OAAA;cAAK0J,KAAK,EAAE;gBAAEyE,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAM,CAAE;cAAAhF,QAAA,gBACnErJ,OAAA,CAACb,QAAQ;gBAAC+D,KAAK,EAAC,eAAe;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpDzJ,OAAA,CAACb,QAAQ;gBAAC+D,KAAK,EAAC,gBAAgB;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACrDzJ,OAAA,CAACb,QAAQ;gBAAC+D,KAAK,EAAC,gBAAgB;gBAAAmG,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtDzJ,OAAA,CAACb,QAAQ;gBAAC+D,KAAK,EAAC,cAAc;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEX3H,gBAAgB,iBACf9B,OAAA;UAAK0J,KAAK,EAAE;YAAE4E,UAAU,EAAE,SAAS;YAAEb,OAAO,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEP,YAAY,EAAE;UAAO,CAAE;UAAA3E,QAAA,eAChGrJ,OAAA;YAAG0J,KAAK,EAAE;cAAEE,MAAM,EAAE,CAAC;cAAEmE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACzDvH;UAAgB;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAEDzJ,OAAA;UAAK0J,KAAK,EAAE;YAAE4E,UAAU,EAAE,SAAS;YAAEb,OAAO,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,SAAS,EAAE;UAAO,CAAE;UAAArE,QAAA,gBAC7FrJ,OAAA;YAAG0J,KAAK,EAAE;cAAEE,MAAM,EAAE,CAAC;cAAEmE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,eACvDrJ,OAAA;cAAAqJ,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJzJ,OAAA;YAAI0J,KAAK,EAAE;cAAEE,MAAM,EAAE,WAAW;cAAED,WAAW,EAAE,MAAM;cAAEoE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,gBACvFrJ,OAAA;cAAAqJ,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRzJ,OAAA,CAACvB,KAAK;MACJ0K,KAAK,EAAE,aAAa9G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmF,IAAI,EAAG;MAC1CoF,IAAI,EAAElK,sBAAuB;MAC7BmK,QAAQ,EAAEhC,0BAA2B;MACrCZ,IAAI,EAAEA,CAAA,KAAMrH,cAAc,CAACkK,MAAM,CAAC,CAAE;MACpC/C,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfiE,cAAc,EAAE5L,aAAa,GAAGE,qBAAqB,CAACwJ,GAAG,CAAC1J,aAAa,CAAC0B,EAAE,CAAC,GAAG,KAAM;MACpFwJ,KAAK,EAAE,GAAI;MAAAlE,QAAA,eAEXrJ,OAAA,CAACtB,IAAI;QACHkC,IAAI,EAAEgC,cAAe;QACrBmK,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExC,sBAAuB;QAAAnB,QAAA,gBAEjCrJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,YAAY;UACjBrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACrB,KAAK;YAACgN,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZzJ,OAAA,CAACtB,IAAI,CAACuO,IAAI;UACRzF,IAAI,EAAC,UAAU;UACfrE,KAAK,EAAC,0BAAM;UACZ+J,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwK,QAAA,eAEhDrJ,OAAA,CAACrB,KAAK;YAACgN,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZzJ,OAAA;UAAK0J,KAAK,EAAE;YAAE4E,UAAU,EAAE,SAAS;YAAEb,OAAO,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,SAAS,EAAE;UAAO,CAAE;UAAArE,QAAA,gBAC7FrJ,OAAA;YAAG0J,KAAK,EAAE;cAAEE,MAAM,EAAE,CAAC;cAAEmE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,eACvDrJ,OAAA;cAAAqJ,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJzJ,OAAA;YAAI0J,KAAK,EAAE;cAAEE,MAAM,EAAE,WAAW;cAAED,WAAW,EAAE,MAAM;cAAEoE,QAAQ,EAAE,MAAM;cAAElE,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,gBACvFrJ,OAAA;cAAAqJ,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzJ,OAAA;cAAAqJ,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRzJ,OAAA,CAACvB,KAAK;MACJ0K,KAAK,EAAE,YAAY9G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmF,IAAI,EAAG;MACzCoF,IAAI,EAAEzK,sBAAuB;MAC7B0K,QAAQ,EAAEA,CAAA,KAAM;QACdzK,yBAAyB,CAAC,KAAK,CAAC;QAChCE,gBAAgB,CAAC,IAAI,CAAC;QACtBU,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAE;MACFiH,IAAI,EAAEtB,0BAA2B;MACjCoB,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfiE,cAAc,EAAE5L,aAAa,GAAGE,qBAAqB,CAACwJ,GAAG,CAAC1J,aAAa,CAAC0B,EAAE,CAAC,GAAG,KAAM;MACpFwJ,KAAK,EAAE,GAAI;MAAAlE,QAAA,gBAEXrJ,OAAA;QAAK0J,KAAK,EAAE;UAAEsE,YAAY,EAAE;QAAG,CAAE;QAAA3E,QAAA,gBAC/BrJ,OAAA;UAAAqJ,QAAA,EAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnBzJ,OAAA,CAACpB,MAAM;UACL8K,KAAK,EAAE;YAAE6D,KAAK,EAAE;UAAO,CAAE;UACzBH,WAAW,EAAC,4CAAS;UACrBlK,KAAK,EAAEH,mBAAoB;UAC3ByL,QAAQ,EAAExL,sBAAuB;UACjCyL,UAAU;UACVC,gBAAgB,EAAC,UAAU;UAAArF,QAAA,EAE1BxG,UAAU,CAACwK,GAAG,CAACsB,GAAG,iBACjB3O,OAAA,CAACC,MAAM;YAAciD,KAAK,EAAEyL,GAAG,CAAC5K,EAAG;YAAAsF,QAAA,GAChCsF,GAAG,CAACnH,IAAI,EAAC,KAAG,EAACmH,GAAG,CAACC,YAAY;UAAA,GADnBD,GAAG,CAAC5K,EAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzJ,OAAA;QAAK0J,KAAK,EAAE;UAAE4E,UAAU,EAAE,SAAS;UAAEb,OAAO,EAAE,MAAM;UAAEc,YAAY,EAAE;QAAM,CAAE;QAAAlF,QAAA,gBAC1ErJ,OAAA;UAAG0J,KAAK,EAAE;YAAEE,MAAM,EAAE,CAAC;YAAEmE,QAAQ,EAAE,MAAM;YAAElE,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,eACvDrJ,OAAA;YAAAqJ,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJzJ,OAAA;UAAI0J,KAAK,EAAE;YAAEE,MAAM,EAAE,WAAW;YAAED,WAAW,EAAE,MAAM;YAAEoE,QAAQ,EAAE,MAAM;YAAElE,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACvFrJ,OAAA;YAAAqJ,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzJ,OAAA;YAAAqJ,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCzJ,OAAA;YAAAqJ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtJ,EAAA,CAx8BID,aAAuB;EAAA,QAKZxB,IAAI,CAACmC,OAAO,EAcJnC,IAAI,CAACmC,OAAO,EAUVnC,IAAI,CAACmC,OAAO;AAAA;AAAAgO,EAAA,GA7BjC3O,aAAuB;AA08B7B,eAAeA,aAAa;AAAC,IAAA2O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}