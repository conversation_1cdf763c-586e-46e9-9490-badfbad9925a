{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tag, Popconfirm, Image, Spin } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, TableOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst AccountManage = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuModalVisible, setFeishuModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState(null);\n  const [feishuLoading, setFeishuLoading] = useState(false);\n  const [feishuSyncModalVisible, setFeishuSyncModalVisible] = useState(false);\n  const [feishuSyncForm] = Form.useForm();\n  const platformOptions = [{\n    value: 'wechat_mp',\n    label: '微信公众号'\n  }, {\n    value: 'wechat_service',\n    label: '微信服务号'\n  }, {\n    value: 'xiaohongshu',\n    label: '小红书'\n  }];\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = account => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败');\n    }\n  };\n  const handleLogin = async account => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n  const startLoginStatusPolling = accountId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const {\n          logged_in\n        } = response.data;\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = account => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n    setDownloadModalVisible(true);\n  };\n  const handleDownloadSubmit = async values => {\n    if (!downloadAccount) return;\n    setDownloadLoading(true);\n    try {\n      var _values$busi, _values$tmpl;\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: ((_values$busi = values.busi) === null || _values$busi === void 0 ? void 0 : _values$busi.toString()) || '3',\n        tmpl: ((_values$tmpl = values.tmpl) === null || _values$tmpl === void 0 ? void 0 : _values$tmpl.toString()) || '19'\n      });\n\n      // 使用相对路径，让axios的baseURL生效\n      const downloadUrl = `http://localhost:8000/api/wechat/download-data/${downloadAccount.id}?${params}`;\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          console.log('下载URL:', downloadUrl);\n\n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          console.log('响应状态:', response.status);\n          console.log('响应头:', Object.fromEntries(response.headers.entries()));\n          if (!response.ok) {\n            const errorText = await response.text();\n            console.error('错误响应:', errorText);\n            throw new Error(`下载失败: ${response.status} - ${errorText}`);\n          }\n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n\n          // 创建下载链接\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          message.success('数据下载成功');\n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/wechat/logout-all');\n      const {\n        success,\n        message: msg,\n        logout_results\n      } = response.data;\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter(r => r.success).length;\n        const totalCount = logout_results.length;\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const handleCreateFeishuBitable = async account => {\n    try {\n      setFeishuLoading(true);\n      const response = await api.post(`/feishu/create-bitable/${account.id}`, {\n        bitable_name: `${account.name}_数据分析表格`\n      });\n      if (response.data.success) {\n        message.success('飞书多维表格创建成功！');\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('创建飞书多维表格失败');\n      }\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('创建飞书表格失败:', error);\n      message.error(`创建失败: ${((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || '未知错误'}`);\n    } finally {\n      setFeishuLoading(false);\n    }\n  };\n  const handleSyncToFeishu = account => {\n    if (!account.feishu_app_token) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n    setFeishuAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n\n    feishuSyncForm.setFieldsValue({\n      begin_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0]\n    });\n    setFeishuSyncModalVisible(true);\n  };\n  const handleFeishuSyncSubmit = async values => {\n    if (!feishuAccount) return;\n    try {\n      setFeishuLoading(true);\n      const response = await api.post(`/feishu/sync-data/${feishuAccount.id}`, {\n        begin_date: values.begin_date,\n        end_date: values.end_date\n      });\n      if (response.data.success) {\n        const {\n          sync_results,\n          data_summary\n        } = response.data;\n        message.success(`数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`);\n        setFeishuSyncModalVisible(false);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || '未知错误'}`);\n    } finally {\n      setFeishuLoading(false);\n    }\n  };\n  const handleFeishuSyncModalClose = () => {\n    setFeishuSyncModalVisible(false);\n    setFeishuAccount(null);\n    feishuSyncForm.resetFields();\n  };\n  const columns = [{\n    title: '账号名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '平台类型',\n    dataIndex: 'platform',\n    key: 'platform',\n    render: platform => {\n      const option = platformOptions.find(opt => opt.value === platform);\n      return option ? option.label : platform;\n    }\n  }, {\n    title: '登录状态',\n    dataIndex: 'login_status',\n    key: 'login_status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status ? 'green' : 'red',\n      children: status ? '已登录' : '未登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后登录时间',\n    dataIndex: 'last_login_time',\n    key: 'last_login_time',\n    render: time => time ? new Date(time).toLocaleString() : '-'\n  }, {\n    title: '飞书状态',\n    key: 'feishu_status',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Tag, {\n      color: record.feishu_app_token ? 'blue' : 'default',\n      children: record.feishu_app_token ? '已创建' : '未创建'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleLogin(record),\n        disabled: record.login_status,\n        children: \"\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDownload(record),\n        disabled: !record.login_status,\n        children: \"\\u4E0B\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this), !record.feishu_app_token ? /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(TableOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleCreateFeishuBitable(record),\n        loading: feishuLoading,\n        disabled: feishuLoading,\n        children: \"\\u521B\\u5EFA\\u98DE\\u4E66\\u8868\\u683C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(CloudSyncOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleSyncToFeishu(record),\n        disabled: !record.login_status || feishuLoading,\n        loading: feishuLoading,\n        children: \"\\u540C\\u6B65\\u5230\\u98DE\\u4E66\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u8FD9\\u4E2A\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n        description: \"\\u6CE8\\u9500\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u626B\\u7801\\u767B\\u5F55\",\n        onConfirm: () => handleLogout(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          loading: logoutLoading === record.id,\n          disabled: !record.login_status || logoutLoading === record.id,\n          children: \"\\u6CE8\\u9500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8D26\\u53F7\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8D26\\u53F7\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u6240\\u6709\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n          description: \"\\u8FD9\\u5C06\\u6CE8\\u9500\\u5F53\\u524D\\u7528\\u6237\\u7684\\u6240\\u6709\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u8D26\\u53F7\\u767B\\u5F55\\u72B6\\u6001\",\n          onConfirm: handleLogoutAll,\n          okText: \"\\u786E\\u5B9A\",\n          cancelText: \"\\u53D6\\u6D88\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 23\n            }, this),\n            loading: loading,\n            disabled: accounts.length === 0 || !accounts.some(acc => acc.login_status),\n            children: \"\\u6CE8\\u9500\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 21\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u6DFB\\u52A0\\u8D26\\u53F7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: accounts,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAccount ? '编辑账号' : '添加账号',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8D26\\u53F7\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入账号名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D26\\u53F7\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"platform\",\n          label: \"\\u5E73\\u53F0\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择平台类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E73\\u53F0\\u7C7B\\u578B\",\n            children: platformOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `登录 ${loginAccount === null || loginAccount === void 0 ? void 0 : loginAccount.name}`,\n      open: loginModalVisible,\n      onCancel: handleLoginModalClose,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoginModalClose,\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => loginAccount && handleLogin(loginAccount),\n        disabled: loginLoading || loginStatus === 'success',\n        children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801\"\n      }, \"retry\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 11\n      }, this)],\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px 0'\n        },\n        children: [loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16\n            },\n            children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this), loginStatus === 'scanning' && qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: qrCodeUrl,\n            alt: \"\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801\",\n            width: 200,\n            height: 200,\n            style: {\n              border: '1px solid #d9d9d9'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16,\n              color: '#1890ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this), \" \\u8BF7\\u4F7F\\u7528\\u5FAE\\u4FE1\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\\u767B\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: \"\\u4E8C\\u7EF4\\u7801\\u5C06\\u572830\\u79D2\\u540E\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 13\n        }, this), loginStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#52c41a',\n              marginBottom: 16\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u6210\\u529F\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 13\n        }, this), loginStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#ff4d4f',\n              marginBottom: 16\n            },\n            children: \"\\u2717\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 13\n        }, this), loginStatus === 'waiting' && !loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {\n            style: {\n              fontSize: '48px',\n              color: '#d9d9d9',\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u51C6\\u5907\\u83B7\\u53D6\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `下载数据 - ${downloadAccount === null || downloadAccount === void 0 ? void 0 : downloadAccount.name}`,\n      open: downloadModalVisible,\n      onCancel: handleDownloadModalClose,\n      onOk: () => downloadForm.submit(),\n      okText: \"\\u4E0B\\u8F7D\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: downloadLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: downloadForm,\n        layout: \"vertical\",\n        onFinish: handleDownloadSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"start_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"busi\",\n          label: \"\\u4E1A\\u52A1\\u7C7B\\u578B\",\n          initialValue: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 3,\n              children: \"\\u9ED8\\u8BA4\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tmpl\",\n          label: \"\\u6A21\\u677F\\u7C7B\\u578B\",\n          initialValue: 19,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 19,\n              children: \"\\u9ED8\\u8BA4\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u6A21\\u677F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u6A21\\u677F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u5C06\\u4E0B\\u8F7D\\u9009\\u5B9A\\u65F6\\u95F4\\u8303\\u56F4\\u5185\\u7684\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u6587\\u4EF6\\u683C\\u5F0F\\u4E3AExcel(.xlsx)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4E0B\\u8F7D\\u65F6\\u95F4\\u53EF\\u80FD\\u8F83\\u957F\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `同步数据到飞书 - ${feishuAccount === null || feishuAccount === void 0 ? void 0 : feishuAccount.name}`,\n      open: feishuSyncModalVisible,\n      onCancel: handleFeishuSyncModalClose,\n      onOk: () => feishuSyncForm.submit(),\n      okText: \"\\u5F00\\u59CB\\u540C\\u6B65\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: feishuLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: feishuSyncForm,\n        layout: \"vertical\",\n        onFinish: handleFeishuSyncSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"begin_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u5C06\\u4E0B\\u8F7D\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u6570\\u636E\\u5E76\\u81EA\\u52A8\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\u591A\\u7EF4\\u8868\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4F1A\\u81EA\\u52A8\\u521B\\u5EFA\\u7528\\u6237\\u6982\\u51B5\\u548C\\u56FE\\u6587\\u5206\\u6790\\u4E24\\u4E2A\\u6570\\u636E\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u4E14\\u5DF2\\u521B\\u5EFA\\u98DE\\u4E66\\u8868\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u540C\\u6B65\\u65F6\\u95F4\\u53EF\\u80FD\\u8F83\\u957F\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 594,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountManage, \"4JAPif12JQsmNBvzNuloTs+5Zrw=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AccountManage;\nexport default AccountManage;\nvar _c;\n$RefreshReg$(_c, \"AccountManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Tag", "Popconfirm", "Image", "Spin", "PlusOutlined", "EditOutlined", "DeleteOutlined", "LoginOutlined", "QrcodeOutlined", "DownloadOutlined", "LogoutOutlined", "CloudSyncOutlined", "TableOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "AccountManage", "_s", "accounts", "setAccounts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingAccount", "setEditingAccount", "form", "useForm", "loginModalVisible", "setLoginModalVisible", "loginAccount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "qrCodeUrl", "setQrCodeUrl", "loginLoading", "setLoginLoading", "loginStatus", "setLoginStatus", "downloadModalVisible", "setDownloadModalVisible", "downloadAccount", "setDownloadAccount", "downloadLoading", "setDownloadLoading", "downloadProgress", "setDownloadProgress", "downloadForm", "logoutLoading", "setLogoutLoading", "feishuModalVisible", "setFeishuModalVisible", "feishuAccount", "set<PERSON><PERSON><PERSON>A<PERSON>unt", "feishuLoading", "setFeishuLoading", "feishuSyncModalVisible", "setFeishuSyncModalVisible", "feishuSyncForm", "platformOptions", "value", "label", "fetchAccounts", "response", "get", "data", "error", "handleAdd", "resetFields", "handleEdit", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "delete", "success", "handleSubmit", "values", "put", "post", "_error$response", "_error$response$data", "detail", "handleLogin", "qrcode", "startLoginStatusPolling", "_error$response2", "_error$response2$data", "accountId", "pollInterval", "setInterval", "logged_in", "clearInterval", "console", "setTimeout", "warning", "handleLoginModalClose", "handleDownload", "login_status", "today", "Date", "endDate", "setDate", "getDate", "startDate", "start_date", "toISOString", "split", "end_date", "data_types", "handleDownloadSubmit", "_values$busi", "_values$tmpl", "params", "URLSearchParams", "busi", "toString", "tmpl", "downloadUrl", "token", "localStorage", "getItem", "log", "fetch", "method", "headers", "status", "Object", "fromEntries", "entries", "ok", "errorText", "text", "Error", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDownloadModalClose", "handleLogout", "clear_saved_state", "name", "_error$response3", "_error$response3$data", "handleForceLogout", "_error$response4", "_error$response4$data", "handleLogoutAll", "msg", "logout_results", "length", "successCount", "filter", "r", "totalCount", "_error$response5", "_error$response5$data", "handleCreateFeishuBitable", "bitable_name", "_error$response6", "_error$response6$data", "handleSyncToFeishu", "feishu_app_token", "begin_date", "handleFeishuSyncSubmit", "sync_results", "data_summary", "user_data_synced", "article_data_synced", "_error$response7", "_error$response7$data", "handleFeishuSyncModalClose", "columns", "title", "dataIndex", "key", "render", "platform", "option", "find", "opt", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "time", "toLocaleString", "_", "record", "size", "type", "icon", "onClick", "disabled", "description", "onConfirm", "okText", "cancelText", "danger", "extra", "some", "acc", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "layout", "onFinish", "<PERSON><PERSON>", "rules", "required", "placeholder", "map", "footer", "width", "style", "textAlign", "padding", "marginTop", "src", "alt", "height", "border", "fontSize", "marginBottom", "confirmLoading", "initialValue", "background", "borderRadius", "margin", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Popconfirm,\n  Image,\n  Spin,\n  Checkbox\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, TableOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  platform: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n  feishu_app_token?: string;\n  feishu_table_id?: string;\n  feishu_created_at?: string;\n}\n\nconst AccountManage: React.FC = () => {\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState<Account | null>(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState<Account | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState<string>('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState<number | null>(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuModalVisible, setFeishuModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState<Account | null>(null);\n  const [feishuLoading, setFeishuLoading] = useState(false);\n  const [feishuSyncModalVisible, setFeishuSyncModalVisible] = useState(false);\n  const [feishuSyncForm] = Form.useForm();\n\n  const platformOptions = [\n    { value: 'wechat_mp', label: '微信公众号' },\n    { value: 'wechat_service', label: '微信服务号' },\n    { value: 'xiaohongshu', label: '小红书' },\n  ];\n\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (account: Account) => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleLogin = async (account: Account) => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n\n  const startLoginStatusPolling = (accountId: number) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const { logged_in } = response.data;\n\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = (account: Account) => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n    \n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n    \n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n    \n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n    \n    setDownloadModalVisible(true);\n  };\n\n  const handleDownloadSubmit = async (values: any) => {\n    if (!downloadAccount) return;\n    \n    setDownloadLoading(true);\n    try {\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: values.busi?.toString() || '3',\n        tmpl: values.tmpl?.toString() || '19'\n      });\n      \n      // 使用相对路径，让axios的baseURL生效\n      const downloadUrl = `http://localhost:8000/api/wechat/download-data/${downloadAccount.id}?${params}`;\n      \n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          console.log('下载URL:', downloadUrl);\n          \n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          \n          console.log('响应状态:', response.status);\n          console.log('响应头:', Object.fromEntries(response.headers.entries()));\n          \n          if (!response.ok) {\n            const errorText = await response.text();\n            console.error('错误响应:', errorText);\n            throw new Error(`下载失败: ${response.status} - ${errorText}`);\n          }\n          \n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n          \n          // 创建下载链接\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          \n          message.success('数据下载成功');\n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error: any) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error: any) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error: any) {\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n      \n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n\n      const response = await api.get('/wechat/logout-all');\n\n      const { success, message: msg, logout_results } = response.data;\n\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter((r: any) => r.success).length;\n        const totalCount = logout_results.length;\n\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const handleCreateFeishuBitable = async (account: Account) => {\n    try {\n      setFeishuLoading(true);\n\n      const response = await api.post(`/feishu/create-bitable/${account.id}`, {\n        bitable_name: `${account.name}_数据分析表格`\n      });\n\n      if (response.data.success) {\n        message.success('飞书多维表格创建成功！');\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('创建飞书多维表格失败');\n      }\n    } catch (error: any) {\n      console.error('创建飞书表格失败:', error);\n      message.error(`创建失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setFeishuLoading(false);\n    }\n  };\n\n  const handleSyncToFeishu = (account: Account) => {\n    if (!account.feishu_app_token) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n\n    setFeishuAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n\n    feishuSyncForm.setFieldsValue({\n      begin_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0]\n    });\n\n    setFeishuSyncModalVisible(true);\n  };\n\n  const handleFeishuSyncSubmit = async (values: any) => {\n    if (!feishuAccount) return;\n\n    try {\n      setFeishuLoading(true);\n\n      const response = await api.post(`/feishu/sync-data/${feishuAccount.id}`, {\n        begin_date: values.begin_date,\n        end_date: values.end_date\n      });\n\n      if (response.data.success) {\n        const { sync_results, data_summary } = response.data;\n        message.success(\n          `数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`\n        );\n        setFeishuSyncModalVisible(false);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error: any) {\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setFeishuLoading(false);\n    }\n  };\n\n  const handleFeishuSyncModalClose = () => {\n    setFeishuSyncModalVisible(false);\n    setFeishuAccount(null);\n    feishuSyncForm.resetFields();\n  };\n\n  const columns = [\n    {\n      title: '账号名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '平台类型',\n      dataIndex: 'platform',\n      key: 'platform',\n      render: (platform: string) => {\n        const option = platformOptions.find(opt => opt.value === platform);\n        return option ? option.label : platform;\n      },\n    },\n    {\n      title: '登录状态',\n      dataIndex: 'login_status',\n      key: 'login_status',\n      render: (status: boolean) => (\n        <Tag color={status ? 'green' : 'red'}>\n          {status ? '已登录' : '未登录'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'last_login_time',\n      key: 'last_login_time',\n      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',\n    },\n    {\n      title: '飞书状态',\n      key: 'feishu_status',\n      render: (_: any, record: Account) => (\n        <Tag color={record.feishu_app_token ? 'blue' : 'default'}>\n          {record.feishu_app_token ? '已创建' : '未创建'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: Account) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"primary\" \n            icon={<LoginOutlined />} \n            size=\"small\"\n            onClick={() => handleLogin(record)}\n            disabled={record.login_status}\n          >\n            登录\n          </Button>\n          <Button\n            type=\"default\"\n            icon={<DownloadOutlined />}\n            size=\"small\"\n            onClick={() => handleDownload(record)}\n            disabled={!record.login_status}\n          >\n            下载\n          </Button>\n          {!record.feishu_app_token ? (\n            <Button\n              type=\"default\"\n              icon={<TableOutlined />}\n              size=\"small\"\n              onClick={() => handleCreateFeishuBitable(record)}\n              loading={feishuLoading}\n              disabled={feishuLoading}\n            >\n              创建飞书表格\n            </Button>\n          ) : (\n            <Button\n              type=\"default\"\n              icon={<CloudSyncOutlined />}\n              size=\"small\"\n              onClick={() => handleSyncToFeishu(record)}\n              disabled={!record.login_status || feishuLoading}\n              loading={feishuLoading}\n            >\n              同步到飞书\n            </Button>\n          )}\n          <Popconfirm\n            title=\"确定要注销这个账号的登录状态吗？\"\n            description=\"注销后需要重新扫码登录\"\n            onConfirm={() => handleLogout(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"default\"\n              icon={<LogoutOutlined />} \n              size=\"small\"\n              loading={logoutLoading === record.id}\n              disabled={!record.login_status || logoutLoading === record.id}\n            >\n              注销\n            </Button>\n          </Popconfirm>\n          <Button \n            icon={<EditOutlined />} \n            size=\"small\"\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个账号吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              danger \n              icon={<DeleteOutlined />} \n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card \n        title=\"账号管理\" \n        extra={\n          <Space>\n            <Popconfirm\n              title=\"确定要注销所有账号的登录状态吗？\"\n              description=\"这将注销当前用户的所有微信公众号账号登录状态\"\n              onConfirm={handleLogoutAll}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button \n                type=\"default\"\n                icon={<LogoutOutlined />}\n                loading={loading}\n                disabled={accounts.length === 0 || !accounts.some(acc => acc.login_status)}\n              >\n                注销全部\n              </Button>\n            </Popconfirm>\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加账号\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={accounts}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAccount ? '编辑账号' : '添加账号'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"账号名称\"\n            rules={[{ required: true, message: '请输入账号名称' }]}\n          >\n            <Input placeholder=\"请输入账号名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"platform\"\n            label=\"平台类型\"\n            rules={[{ required: true, message: '请选择平台类型' }]}\n          >\n            <Select placeholder=\"请选择平台类型\">\n              {platformOptions.map(option => (\n                <Option key={option.value} value={option.value}>\n                  {option.label}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 登录模态框 */}\n      <Modal\n        title={`登录 ${loginAccount?.name}`}\n        open={loginModalVisible}\n        onCancel={handleLoginModalClose}\n        footer={[\n          <Button key=\"close\" onClick={handleLoginModalClose}>\n            关闭\n          </Button>,\n          <Button\n            key=\"retry\"\n            type=\"primary\"\n            onClick={() => loginAccount && handleLogin(loginAccount)}\n            disabled={loginLoading || loginStatus === 'success'}\n          >\n            重新获取二维码\n          </Button>\n        ]}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          {loginLoading && (\n            <div>\n              <Spin size=\"large\" />\n              <p style={{ marginTop: 16 }}>正在获取二维码...</p>\n            </div>\n          )}\n\n          {loginStatus === 'scanning' && qrCodeUrl && (\n            <div>\n              <Image\n                src={qrCodeUrl}\n                alt=\"登录二维码\"\n                width={200}\n                height={200}\n                style={{ border: '1px solid #d9d9d9' }}\n              />\n              <p style={{ marginTop: 16, color: '#1890ff' }}>\n                <QrcodeOutlined /> 请使用微信扫描二维码登录\n              </p>\n              <p style={{ color: '#666', fontSize: '12px' }}>\n                二维码将在30秒后过期\n              </p>\n            </div>\n          )}\n\n          {loginStatus === 'success' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>\n                ✓\n              </div>\n              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>\n            </div>\n          )}\n\n          {loginStatus === 'failed' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>\n                ✗\n              </div>\n              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>\n            </div>\n          )}\n\n          {loginStatus === 'waiting' && !loginLoading && (\n            <div>\n              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />\n              <p style={{ color: '#666' }}>准备获取登录二维码...</p>\n            </div>\n          )}\n        </div>\n      </Modal>\n\n      {/* 下载数据模态框 */}\n      <Modal\n        title={`下载数据 - ${downloadAccount?.name}`}\n        open={downloadModalVisible}\n        onCancel={handleDownloadModalClose}\n        onOk={() => downloadForm.submit()}\n        okText=\"下载\"\n        cancelText=\"取消\"\n        confirmLoading={downloadLoading}\n        width={500}\n      >\n        <Form\n          form={downloadForm}\n          layout=\"vertical\"\n          onFinish={handleDownloadSubmit}\n        >\n          <Form.Item\n            name=\"start_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"busi\"\n            label=\"业务类型\"\n            initialValue={3}\n          >\n            <Select>\n              <Option value={3}>默认业务</Option>\n              <Option value={1}>其他业务1</Option>\n              <Option value={2}>其他业务2</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"tmpl\"\n            label=\"模板类型\"\n            initialValue={19}\n          >\n            <Select>\n              <Option value={19}>默认模板</Option>\n              <Option value={1}>模板1</Option>\n              <Option value={2}>模板2</Option>\n            </Select>\n          </Form.Item>\n          \n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>将下载选定时间范围内的微信公众号数据</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>文件格式为Excel(.xlsx)</li>\n              <li>请确保账号已登录状态</li>\n              <li>下载时间可能较长，请耐心等待</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n\n      {/* 飞书数据同步模态框 */}\n      <Modal\n        title={`同步数据到飞书 - ${feishuAccount?.name}`}\n        open={feishuSyncModalVisible}\n        onCancel={handleFeishuSyncModalClose}\n        onOk={() => feishuSyncForm.submit()}\n        okText=\"开始同步\"\n        cancelText=\"取消\"\n        confirmLoading={feishuLoading}\n        width={500}\n      >\n        <Form\n          form={feishuSyncForm}\n          layout=\"vertical\"\n          onFinish={handleFeishuSyncSubmit}\n        >\n          <Form.Item\n            name=\"begin_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n\n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>将下载微信公众号数据并自动同步到飞书多维表格</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>会自动创建用户概况和图文分析两个数据表</li>\n              <li>请确保账号已登录且已创建飞书表格</li>\n              <li>同步时间可能较长，请耐心等待</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AccountManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,QAEC,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,mBAAmB;AACjL,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAO,CAAC,GAAGnB,MAAM;AAczB,MAAMoB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACsC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAiB,IAAI,CAAC;EACtE,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAS,SAAS,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACkD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC0D,YAAY,CAAC,GAAGpD,IAAI,CAACiC,OAAO,CAAC,CAAC;;EAErC;EACA,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAAC6D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAiB,IAAI,CAAC;EACxE,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACqE,cAAc,CAAC,GAAG/D,IAAI,CAACiC,OAAO,CAAC,CAAC;EAEvC,MAAM+B,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAM,CAAC,CACvC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCxC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,YAAY,CAAC;MAC5C5C,WAAW,CAAC2C,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACdwE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtBzC,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACyC,WAAW,CAAC,CAAC;IAClB5C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM6C,UAAU,GAAIC,OAAgB,IAAK;IACvC5C,iBAAiB,CAAC4C,OAAO,CAAC;IAC1B3C,IAAI,CAAC4C,cAAc,CAACD,OAAO,CAAC;IAC5B9C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgD,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM5D,GAAG,CAAC6D,MAAM,CAAC,aAAaD,EAAE,EAAE,CAAC;MACnC3E,OAAO,CAAC6E,OAAO,CAAC,MAAM,CAAC;MACvBb,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAIpD,cAAc,EAAE;QAClB,MAAMZ,GAAG,CAACiE,GAAG,CAAC,aAAarD,cAAc,CAACgD,EAAE,EAAE,EAAEI,MAAM,CAAC;QACvD/E,OAAO,CAAC6E,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAM9D,GAAG,CAACkE,IAAI,CAAC,YAAY,EAAEF,MAAM,CAAC;QACpC/E,OAAO,CAAC6E,OAAO,CAAC,MAAM,CAAC;MACzB;MACAnD,eAAe,CAAC,KAAK,CAAC;MACtBsC,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnBnF,OAAO,CAACoE,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOb,OAAgB,IAAK;IAC9CtC,eAAe,CAACsC,OAAO,CAAC;IACxBxC,oBAAoB,CAAC,IAAI,CAAC;IAC1BQ,cAAc,CAAC,SAAS,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM2B,QAAQ,GAAG,MAAMlD,GAAG,CAACkE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MACrE,IAAIV,QAAQ,CAACE,IAAI,CAACmB,MAAM,EAAE;QACxBlD,YAAY,CAAC6B,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC;QAClC9C,cAAc,CAAC,UAAU,CAAC;;QAE1B;QACA+C,uBAAuB,CAACf,OAAO,CAACG,EAAE,CAAC;MACrC,CAAC,MAAM;QACL3E,OAAO,CAACoE,KAAK,CAAC,SAAS,CAAC;QACxB5B,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAO4B,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBzF,OAAO,CAACoE,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,SAAS,CAAC;MACxD5C,cAAc,CAAC,QAAQ,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMiD,uBAAuB,GAAIG,SAAiB,IAAK;IACrD,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,wBAAwBwB,SAAS,EAAE,CAAC;QACnE,MAAM;UAAEG;QAAU,CAAC,GAAG5B,QAAQ,CAACE,IAAI;QAEnC,IAAI0B,SAAS,EAAE;UACbrD,cAAc,CAAC,SAAS,CAAC;UACzBxC,OAAO,CAAC6E,OAAO,CAAC,OAAO,CAAC;UACxBiB,aAAa,CAACH,YAAY,CAAC;UAC3B3D,oBAAoB,CAAC,KAAK,CAAC;UAC3BgC,aAAa,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA4B,UAAU,CAAC,MAAM;MACfF,aAAa,CAACH,YAAY,CAAC;MAC3B,IAAIpD,WAAW,KAAK,UAAU,EAAE;QAC9BC,cAAc,CAAC,QAAQ,CAAC;QACxBxC,OAAO,CAACiG,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClClE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM2D,cAAc,GAAI3B,OAAgB,IAAK;IAC3C,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;MACzBpG,OAAO,CAACiG,OAAO,CAAC,cAAc,CAAC;MAC/B;IACF;IACArD,kBAAkB,CAAC4B,OAAO,CAAC;;IAE3B;IACA,MAAM6B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAC/BE,OAAO,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEtC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACC,OAAO,CAAC;IACnCG,SAAS,CAACF,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;IAE3CxD,YAAY,CAACwB,cAAc,CAAC;MAC1BkC,UAAU,EAAED,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAEP,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CE,UAAU,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACpF,CAAC,CAAC;IAEFrE,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMsE,oBAAoB,GAAG,MAAOjC,MAAW,IAAK;IAClD,IAAI,CAACpC,eAAe,EAAE;IAEtBG,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MAAA,IAAAmE,YAAA,EAAAC,YAAA;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCT,UAAU,EAAE5B,MAAM,CAAC4B,UAAU;QAC7BG,QAAQ,EAAE/B,MAAM,CAAC+B,QAAQ;QACzBO,IAAI,EAAE,EAAAJ,YAAA,GAAAlC,MAAM,CAACsC,IAAI,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,QAAQ,CAAC,CAAC,KAAI,GAAG;QACpCC,IAAI,EAAE,EAAAL,YAAA,GAAAnC,MAAM,CAACwC,IAAI,cAAAL,YAAA,uBAAXA,YAAA,CAAaI,QAAQ,CAAC,CAAC,KAAI;MACnC,CAAC,CAAC;;MAEF;MACA,MAAME,WAAW,GAAG,kDAAkD7E,eAAe,CAACgC,EAAE,IAAIwC,MAAM,EAAE;MAEpG,MAAMM,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACF1B,OAAO,CAAC6B,GAAG,CAAC,QAAQ,EAAEJ,WAAW,CAAC;;UAElC;UACA,MAAMvD,QAAQ,GAAG,MAAM4D,KAAK,CAACL,WAAW,EAAE;YACxCM,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUN,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UAEF1B,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAE3D,QAAQ,CAAC+D,MAAM,CAAC;UACrCjC,OAAO,CAAC6B,GAAG,CAAC,MAAM,EAAEK,MAAM,CAACC,WAAW,CAACjE,QAAQ,CAAC8D,OAAO,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;UAEnE,IAAI,CAAClE,QAAQ,CAACmE,EAAE,EAAE;YAChB,MAAMC,SAAS,GAAG,MAAMpE,QAAQ,CAACqE,IAAI,CAAC,CAAC;YACvCvC,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEiE,SAAS,CAAC;YACjC,MAAM,IAAIE,KAAK,CAAC,SAAStE,QAAQ,CAAC+D,MAAM,MAAMK,SAAS,EAAE,CAAC;UAC5D;UAEA,MAAMG,IAAI,GAAG,MAAMvE,QAAQ,CAACuE,IAAI,CAAC,CAAC;UAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;UAE5C;UACA,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,eAAelE,MAAM,CAAC4B,UAAU,OAAO5B,MAAM,CAAC+B,QAAQ,OAAO;UAC7EgC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;UAE/BzI,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;UACzB7E,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;UACzBnC,uBAAuB,CAAC,KAAK,CAAC;QAChC,CAAC,CAAC,OAAO0B,KAAU,EAAE;UACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;UAC7BpE,OAAO,CAACoE,KAAK,CAAC,SAASA,KAAK,CAACpE,OAAO,EAAE,CAAC;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpE,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyG,wBAAwB,GAAGA,CAAA,KAAM;IACrC7G,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;IACxBK,YAAY,CAACqB,WAAW,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkF,YAAY,GAAG,MAAOhF,OAAgB,IAAK;IAC/C,IAAI;MACFrB,gBAAgB,CAACqB,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMlD,GAAG,CAACkE,IAAI,CAAC,kBAAkBT,OAAO,CAACG,EAAE,EAAE,EAAE;QAC9D8E,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIxF,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB7E,OAAO,CAAC6E,OAAO,CAAC,MAAML,OAAO,CAACkF,IAAI,OAAO,CAAC;QAC1C;QACA1F,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLhE,OAAO,CAACiG,OAAO,CAAC,MAAMzB,OAAO,CAACkF,IAAI,iBAAiB,CAAC;QACpD;QACA1F,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAuF,gBAAA,EAAAC,qBAAA;MACnB7D,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpE,OAAO,CAACoE,KAAK,CAAC,SAAS,EAAAuF,gBAAA,GAAAvF,KAAK,CAACH,QAAQ,cAAA0F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxF,IAAI,cAAAyF,qBAAA,uBAApBA,qBAAA,CAAsBxE,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACRjC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM0G,iBAAiB,GAAG,MAAOrF,OAAgB,IAAK;IACpD,IAAI;MACFrB,gBAAgB,CAACqB,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMlD,GAAG,CAACkE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MAErE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB7E,OAAO,CAAC6E,OAAO,CAAC,MAAML,OAAO,CAACkF,IAAI,SAAS,CAAC;MAC9C,CAAC,MAAM;QACL1J,OAAO,CAACiG,OAAO,CAAC,MAAMzB,OAAO,CAACkF,IAAI,SAAS,CAAC;MAC9C;;MAEA;MACA1F,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAA0F,gBAAA,EAAAC,qBAAA;MACnBhE,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,WAAW,EAAA0F,gBAAA,GAAA1F,KAAK,CAACH,QAAQ,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3F,IAAI,cAAA4F,qBAAA,uBAApBA,qBAAA,CAAsB3E,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRjC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM6G,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFxI,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMyC,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,oBAAoB,CAAC;MAEpD,MAAM;QAAEW,OAAO;QAAE7E,OAAO,EAAEiK,GAAG;QAAEC;MAAe,CAAC,GAAGjG,QAAQ,CAACE,IAAI;MAE/D,IAAIU,OAAO,EAAE;QACX7E,OAAO,CAAC6E,OAAO,CAACoF,GAAG,CAAC;MACtB,CAAC,MAAM;QACLjK,OAAO,CAACiG,OAAO,CAACgE,GAAG,CAAC;MACtB;;MAEA;MACA,IAAIC,cAAc,IAAIA,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAMC,YAAY,GAAGF,cAAc,CAACG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACzF,OAAO,CAAC,CAACsF,MAAM;QACxE,MAAMI,UAAU,GAAGL,cAAc,CAACC,MAAM;QAExC,IAAIC,YAAY,KAAKG,UAAU,EAAE;UAC/BvK,OAAO,CAAC6E,OAAO,CAAC,MAAM0F,UAAU,UAAU,CAAC;QAC7C,CAAC,MAAM;UACLvK,OAAO,CAACiG,OAAO,CAAC,GAAGmE,YAAY,IAAIG,UAAU,UAAU,CAAC;QAC1D;MACF;;MAEA;MACAvG,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAoG,gBAAA,EAAAC,qBAAA;MACnB1E,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,WAAW,EAAAoG,gBAAA,GAAApG,KAAK,CAACH,QAAQ,cAAAuG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrG,IAAI,cAAAsG,qBAAA,uBAApBA,qBAAA,CAAsBrF,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACR5D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkJ,yBAAyB,GAAG,MAAOlG,OAAgB,IAAK;IAC5D,IAAI;MACFf,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAMQ,QAAQ,GAAG,MAAMlD,GAAG,CAACkE,IAAI,CAAC,0BAA0BT,OAAO,CAACG,EAAE,EAAE,EAAE;QACtEgG,YAAY,EAAE,GAAGnG,OAAO,CAACkF,IAAI;MAC/B,CAAC,CAAC;MAEF,IAAIzF,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB7E,OAAO,CAAC6E,OAAO,CAAC,aAAa,CAAC;QAC9Bb,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLhE,OAAO,CAACoE,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAwG,gBAAA,EAAAC,qBAAA;MACnB9E,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpE,OAAO,CAACoE,KAAK,CAAC,SAAS,EAAAwG,gBAAA,GAAAxG,KAAK,CAACH,QAAQ,cAAA2G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzG,IAAI,cAAA0G,qBAAA,uBAApBA,qBAAA,CAAsBzF,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR3B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMqH,kBAAkB,GAAItG,OAAgB,IAAK;IAC/C,IAAI,CAACA,OAAO,CAACuG,gBAAgB,EAAE;MAC7B/K,OAAO,CAACiG,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACA,IAAI,CAACzB,OAAO,CAAC4B,YAAY,EAAE;MACzBpG,OAAO,CAACiG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEA1C,gBAAgB,CAACiB,OAAO,CAAC;;IAEzB;IACA,MAAM6B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAC/BE,OAAO,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEtC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACC,OAAO,CAAC;IACnCG,SAAS,CAACF,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;IAE3C7C,cAAc,CAACa,cAAc,CAAC;MAC5BuG,UAAU,EAAEtE,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAEP,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEFlD,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMsH,sBAAsB,GAAG,MAAOlG,MAAW,IAAK;IACpD,IAAI,CAACzB,aAAa,EAAE;IAEpB,IAAI;MACFG,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAMQ,QAAQ,GAAG,MAAMlD,GAAG,CAACkE,IAAI,CAAC,qBAAqB3B,aAAa,CAACqB,EAAE,EAAE,EAAE;QACvEqG,UAAU,EAAEjG,MAAM,CAACiG,UAAU;QAC7BlE,QAAQ,EAAE/B,MAAM,CAAC+B;MACnB,CAAC,CAAC;MAEF,IAAI7C,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB,MAAM;UAAEqG,YAAY;UAAEC;QAAa,CAAC,GAAGlH,QAAQ,CAACE,IAAI;QACpDnE,OAAO,CAAC6E,OAAO,CACb,gBAAgBqG,YAAY,CAACE,gBAAgB,YAAYF,YAAY,CAACG,mBAAmB,IAC3F,CAAC;QACD1H,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,MAAM;QACL3D,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAkH,gBAAA,EAAAC,qBAAA;MACnBxF,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC,SAAS,EAAAkH,gBAAA,GAAAlH,KAAK,CAACH,QAAQ,cAAAqH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnH,IAAI,cAAAoH,qBAAA,uBAApBA,qBAAA,CAAsBnG,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR3B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM+H,0BAA0B,GAAGA,CAAA,KAAM;IACvC7H,yBAAyB,CAAC,KAAK,CAAC;IAChCJ,gBAAgB,CAAC,IAAI,CAAC;IACtBK,cAAc,CAACU,WAAW,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMmH,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,QAAgB,IAAK;MAC5B,MAAMC,MAAM,GAAGlI,eAAe,CAACmI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnI,KAAK,KAAKgI,QAAQ,CAAC;MAClE,OAAOC,MAAM,GAAGA,MAAM,CAAChI,KAAK,GAAG+H,QAAQ;IACzC;EACF,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAG7D,MAAe,iBACtB/G,OAAA,CAACf,GAAG;MAACgM,KAAK,EAAElE,MAAM,GAAG,OAAO,GAAG,KAAM;MAAAmE,QAAA,EAClCnE,MAAM,GAAG,KAAK,GAAG;IAAK;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAGW,IAAmB,IAAKA,IAAI,GAAG,IAAIlG,IAAI,CAACkG,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC5E,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACa,CAAM,EAAEC,MAAe,kBAC9B1L,OAAA,CAACf,GAAG;MAACgM,KAAK,EAAES,MAAM,CAAC5B,gBAAgB,GAAG,MAAM,GAAG,SAAU;MAAAoB,QAAA,EACtDQ,MAAM,CAAC5B,gBAAgB,GAAG,KAAK,GAAG;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGW,IAAY,IAAK,IAAIlG,IAAI,CAACkG,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACa,CAAM,EAAEC,MAAe,kBAC9B1L,OAAA,CAAChB,KAAK;MAAC2M,IAAI,EAAC,QAAQ;MAAAT,QAAA,gBAClBlL,OAAA,CAACtB,MAAM;QACLkN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE7L,OAAA,CAACR,aAAa;UAAA2L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM1H,WAAW,CAACsH,MAAM,CAAE;QACnCK,QAAQ,EAAEL,MAAM,CAACvG,YAAa;QAAA+F,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtL,OAAA,CAACtB,MAAM;QACLkN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE7L,OAAA,CAACN,gBAAgB;UAAAyL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM5G,cAAc,CAACwG,MAAM,CAAE;QACtCK,QAAQ,EAAE,CAACL,MAAM,CAACvG,YAAa;QAAA+F,QAAA,EAChC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAACI,MAAM,CAAC5B,gBAAgB,gBACvB9J,OAAA,CAACtB,MAAM;QACLkN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE7L,OAAA,CAACH,aAAa;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMrC,yBAAyB,CAACiC,MAAM,CAAE;QACjDpL,OAAO,EAAEiC,aAAc;QACvBwJ,QAAQ,EAAExJ,aAAc;QAAA2I,QAAA,EACzB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETtL,OAAA,CAACtB,MAAM;QACLkN,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE7L,OAAA,CAACJ,iBAAiB;UAAAuL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC6B,MAAM,CAAE;QAC1CK,QAAQ,EAAE,CAACL,MAAM,CAACvG,YAAY,IAAI5C,aAAc;QAChDjC,OAAO,EAAEiC,aAAc;QAAA2I,QAAA,EACxB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACDtL,OAAA,CAACd,UAAU;QACTuL,KAAK,EAAC,kGAAkB;QACxBuB,WAAW,EAAC,oEAAa;QACzBC,SAAS,EAAEA,CAAA,KAAM1D,YAAY,CAACmD,MAAM,CAAE;QACtCQ,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAjB,QAAA,eAEflL,OAAA,CAACtB,MAAM;UACLkN,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE7L,OAAA,CAACL,cAAc;YAAAwL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBK,IAAI,EAAC,OAAO;UACZrL,OAAO,EAAE2B,aAAa,KAAKyJ,MAAM,CAAChI,EAAG;UACrCqI,QAAQ,EAAE,CAACL,MAAM,CAACvG,YAAY,IAAIlD,aAAa,KAAKyJ,MAAM,CAAChI,EAAG;UAAAwH,QAAA,EAC/D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACbtL,OAAA,CAACtB,MAAM;QACLmN,IAAI,eAAE7L,OAAA,CAACV,YAAY;UAAA6L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMxI,UAAU,CAACoI,MAAM,CAAE;QAAAR,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtL,OAAA,CAACd,UAAU;QACTuL,KAAK,EAAC,oEAAa;QACnBwB,SAAS,EAAEA,CAAA,KAAMxI,YAAY,CAACiI,MAAM,CAAChI,EAAE,CAAE;QACzCwI,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAjB,QAAA,eAEflL,OAAA,CAACtB,MAAM;UACL0N,MAAM;UACNP,IAAI,eAAE7L,OAAA,CAACT,cAAc;YAAA4L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBK,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEtL,OAAA;IAAAkL,QAAA,gBACElL,OAAA,CAACxB,IAAI;MACHiM,KAAK,EAAC,0BAAM;MACZ4B,KAAK,eACHrM,OAAA,CAAChB,KAAK;QAAAkM,QAAA,gBACJlL,OAAA,CAACd,UAAU;UACTuL,KAAK,EAAC,kGAAkB;UACxBuB,WAAW,EAAC,sIAAwB;UACpCC,SAAS,EAAElD,eAAgB;UAC3BmD,MAAM,EAAC,cAAI;UACXC,UAAU,EAAC,cAAI;UAAAjB,QAAA,eAEflL,OAAA,CAACtB,MAAM;YACLkN,IAAI,EAAC,SAAS;YACdC,IAAI,eAAE7L,OAAA,CAACL,cAAc;cAAAwL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBhL,OAAO,EAAEA,OAAQ;YACjByL,QAAQ,EAAE3L,QAAQ,CAAC8I,MAAM,KAAK,CAAC,IAAI,CAAC9I,QAAQ,CAACkM,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpH,YAAY,CAAE;YAAA+F,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACbtL,OAAA,CAACtB,MAAM;UACLkN,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE7L,OAAA,CAACX,YAAY;YAAA8L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBQ,OAAO,EAAE1I,SAAU;UAAA8H,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAEDlL,OAAA,CAACvB,KAAK;QACJ+L,OAAO,EAAEA,OAAQ;QACjBgC,UAAU,EAAEpM,QAAS;QACrBqM,MAAM,EAAC,IAAI;QACXnM,OAAO,EAAEA,OAAQ;QACjBoM,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPtL,OAAA,CAACrB,KAAK;MACJ8L,KAAK,EAAE/J,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCqM,IAAI,EAAEvM,YAAa;MACnBwM,QAAQ,EAAEA,CAAA,KAAMvM,eAAe,CAAC,KAAK,CAAE;MACvCwM,IAAI,EAAEA,CAAA,KAAMrM,IAAI,CAACsM,MAAM,CAAC,CAAE;MAC1BhB,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAjB,QAAA,eAEflL,OAAA,CAACpB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACXuM,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEvJ,YAAa;QAAAqH,QAAA,gBAEvBlL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,MAAM;UACX3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAACnB,KAAK;YAAC2O,WAAW,EAAC;UAAS;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZtL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,UAAU;UACf3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAAClB,MAAM;YAAC0O,WAAW,EAAC,4CAAS;YAAAtC,QAAA,EAC1BtI,eAAe,CAAC6K,GAAG,CAAC3C,MAAM,iBACzB9K,OAAA,CAACC,MAAM;cAAoB4C,KAAK,EAAEiI,MAAM,CAACjI,KAAM;cAAAqI,QAAA,EAC5CJ,MAAM,CAAChI;YAAK,GADFgI,MAAM,CAACjI,KAAK;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRtL,OAAA,CAACrB,KAAK;MACJ8L,KAAK,EAAE,MAAMzJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyH,IAAI,EAAG;MAClCsE,IAAI,EAAEjM,iBAAkB;MACxBkM,QAAQ,EAAE/H,qBAAsB;MAChCyI,MAAM,EAAE,cACN1N,OAAA,CAACtB,MAAM;QAAaoN,OAAO,EAAE7G,qBAAsB;QAAAiG,QAAA,EAAC;MAEpD,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTtL,OAAA,CAACtB,MAAM;QAELkN,IAAI,EAAC,SAAS;QACdE,OAAO,EAAEA,CAAA,KAAM9K,YAAY,IAAIoD,WAAW,CAACpD,YAAY,CAAE;QACzD+K,QAAQ,EAAE3K,YAAY,IAAIE,WAAW,KAAK,SAAU;QAAA4J,QAAA,EACrD;MAED,GANM,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CAAC,CACT;MACFqC,KAAK,EAAE,GAAI;MAAAzC,QAAA,eAEXlL,OAAA;QAAK4N,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAA5C,QAAA,GACpD9J,YAAY,iBACXpB,OAAA;UAAAkL,QAAA,gBACElL,OAAA,CAACZ,IAAI;YAACuM,IAAI,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBtL,OAAA;YAAG4N,KAAK,EAAE;cAAEG,SAAS,EAAE;YAAG,CAAE;YAAA7C,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,EAEAhK,WAAW,KAAK,UAAU,IAAIJ,SAAS,iBACtClB,OAAA;UAAAkL,QAAA,gBACElL,OAAA,CAACb,KAAK;YACJ6O,GAAG,EAAE9M,SAAU;YACf+M,GAAG,EAAC,gCAAO;YACXN,KAAK,EAAE,GAAI;YACXO,MAAM,EAAE,GAAI;YACZN,KAAK,EAAE;cAAEO,MAAM,EAAE;YAAoB;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACFtL,OAAA;YAAG4N,KAAK,EAAE;cAAEG,SAAS,EAAE,EAAE;cAAE9C,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,gBAC5ClL,OAAA,CAACP,cAAc;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtL,OAAA;YAAG4N,KAAK,EAAE;cAAE3C,KAAK,EAAE,MAAM;cAAEmD,QAAQ,EAAE;YAAO,CAAE;YAAAlD,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAhK,WAAW,KAAK,SAAS,iBACxBtB,OAAA;UAAAkL,QAAA,gBACElL,OAAA;YAAK4N,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE,SAAS;cAAEoD,YAAY,EAAE;YAAG,CAAE;YAAAnD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtL,OAAA;YAAG4N,KAAK,EAAE;cAAE3C,KAAK,EAAE,SAAS;cAAEmD,QAAQ,EAAE;YAAO,CAAE;YAAAlD,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAhK,WAAW,KAAK,QAAQ,iBACvBtB,OAAA;UAAAkL,QAAA,gBACElL,OAAA;YAAK4N,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE,SAAS;cAAEoD,YAAY,EAAE;YAAG,CAAE;YAAAnD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtL,OAAA;YAAG4N,KAAK,EAAE;cAAE3C,KAAK,EAAE,SAAS;cAAEmD,QAAQ,EAAE;YAAO,CAAE;YAAAlD,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEAhK,WAAW,KAAK,SAAS,IAAI,CAACF,YAAY,iBACzCpB,OAAA;UAAAkL,QAAA,gBACElL,OAAA,CAACP,cAAc;YAACmO,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE,SAAS;cAAEoD,YAAY,EAAE;YAAG;UAAE;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFtL,OAAA;YAAG4N,KAAK,EAAE;cAAE3C,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtL,OAAA,CAACrB,KAAK;MACJ8L,KAAK,EAAE,UAAU/I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+G,IAAI,EAAG;MACzCsE,IAAI,EAAEvL,oBAAqB;MAC3BwL,QAAQ,EAAE1E,wBAAyB;MACnC2E,IAAI,EAAEA,CAAA,KAAMjL,YAAY,CAACkL,MAAM,CAAC,CAAE;MAClChB,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfmC,cAAc,EAAE1M,eAAgB;MAChC+L,KAAK,EAAE,GAAI;MAAAzC,QAAA,eAEXlL,OAAA,CAACpB,IAAI;QACHgC,IAAI,EAAEoB,YAAa;QACnBmL,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAErH,oBAAqB;QAAAmF,QAAA,gBAE/BlL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,YAAY;UACjB3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAACnB,KAAK;YAAC+M,IAAI,EAAC,MAAM;YAAC4B,WAAW,EAAC;UAAS;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,UAAU;UACf3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAACnB,KAAK;YAAC+M,IAAI,EAAC,MAAM;YAAC4B,WAAW,EAAC;UAAS;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,MAAM;UACX3F,KAAK,EAAC,0BAAM;UACZyL,YAAY,EAAE,CAAE;UAAArD,QAAA,eAEhBlL,OAAA,CAAClB,MAAM;YAAAoM,QAAA,gBACLlL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,CAAE;cAAAqI,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/BtL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,CAAE;cAAAqI,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCtL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,CAAE;cAAAqI,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZtL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,MAAM;UACX3F,KAAK,EAAC,0BAAM;UACZyL,YAAY,EAAE,EAAG;UAAArD,QAAA,eAEjBlL,OAAA,CAAClB,MAAM;YAAAoM,QAAA,gBACLlL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,EAAG;cAAAqI,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCtL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,CAAE;cAAAqI,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BtL,OAAA,CAACC,MAAM;cAAC4C,KAAK,EAAE,CAAE;cAAAqI,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZtL,OAAA;UAAK4N,KAAK,EAAE;YAAEY,UAAU,EAAE,SAAS;YAAEV,OAAO,EAAE,MAAM;YAAEW,YAAY,EAAE,KAAK;YAAEV,SAAS,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FlL,OAAA;YAAG4N,KAAK,EAAE;cAAEc,MAAM,EAAE,CAAC;cAAEN,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,eACvDlL,OAAA;cAAAkL,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJtL,OAAA;YAAI4N,KAAK,EAAE;cAAEc,MAAM,EAAE,WAAW;cAAEC,WAAW,EAAE,MAAM;cAAEP,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACvFlL,OAAA;cAAAkL,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BtL,OAAA;cAAAkL,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BtL,OAAA;cAAAkL,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BtL,OAAA;cAAAkL,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBtL,OAAA;cAAAkL,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRtL,OAAA,CAACrB,KAAK;MACJ8L,KAAK,EAAE,aAAapI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoG,IAAI,EAAG;MAC1CsE,IAAI,EAAEtK,sBAAuB;MAC7BuK,QAAQ,EAAEzC,0BAA2B;MACrC0C,IAAI,EAAEA,CAAA,KAAMtK,cAAc,CAACuK,MAAM,CAAC,CAAE;MACpChB,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfmC,cAAc,EAAE/L,aAAc;MAC9BoL,KAAK,EAAE,GAAI;MAAAzC,QAAA,eAEXlL,OAAA,CAACpB,IAAI;QACHgC,IAAI,EAAE+B,cAAe;QACrBwK,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEpD,sBAAuB;QAAAkB,QAAA,gBAEjClL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,YAAY;UACjB3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAACnB,KAAK;YAAC+M,IAAI,EAAC,MAAM;YAAC4B,WAAW,EAAC;UAAS;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtL,OAAA,CAACpB,IAAI,CAACyO,IAAI;UACR5E,IAAI,EAAC,UAAU;UACf3F,KAAK,EAAC,0BAAM;UACZwK,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExO,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmM,QAAA,eAEhDlL,OAAA,CAACnB,KAAK;YAAC+M,IAAI,EAAC,MAAM;YAAC4B,WAAW,EAAC;UAAS;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtL,OAAA;UAAK4N,KAAK,EAAE;YAAEY,UAAU,EAAE,SAAS;YAAEV,OAAO,EAAE,MAAM;YAAEW,YAAY,EAAE,KAAK;YAAEV,SAAS,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FlL,OAAA;YAAG4N,KAAK,EAAE;cAAEc,MAAM,EAAE,CAAC;cAAEN,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,eACvDlL,OAAA;cAAAkL,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJtL,OAAA;YAAI4N,KAAK,EAAE;cAAEc,MAAM,EAAE,WAAW;cAAEC,WAAW,EAAE,MAAM;cAAEP,QAAQ,EAAE,MAAM;cAAEnD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACvFlL,OAAA;cAAAkL,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BtL,OAAA;cAAAkL,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BtL,OAAA;cAAAkL,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BtL,OAAA;cAAAkL,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtL,OAAA;cAAAkL,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnL,EAAA,CA/zBID,aAAuB;EAAA,QAKZtB,IAAI,CAACiC,OAAO,EAcJjC,IAAI,CAACiC,OAAO,EAUVjC,IAAI,CAACiC,OAAO;AAAA;AAAA+N,EAAA,GA7BjC1O,aAAuB;AAi0B7B,eAAeA,aAAa;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}