{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tag, Popconfirm, Image, Spin, Checkbox } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, LinkOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { feishuAppService } from '../services/feishuAppService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst AccountManage = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuBindModalVisible, setFeishuBindModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState(null);\n  const [feishuLoadingAccounts, setFeishuLoadingAccounts] = useState(new Set());\n  const [feishuApps, setFeishuApps] = useState([]);\n  const [selectedFeishuAppId, setSelectedFeishuAppId] = useState(null);\n  const platformOptions = [{\n    value: 'wechat_mp',\n    label: '微信公众号'\n  }, {\n    value: 'wechat_service',\n    label: '微信服务号'\n  }, {\n    value: 'wechat_channels',\n    label: '视频号'\n  }, {\n    value: 'xiaohongshu',\n    label: '小红书'\n  }];\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = account => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败');\n    }\n  };\n  const handleLogin = async account => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n  const startLoginStatusPolling = accountId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const {\n          logged_in\n        } = response.data;\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = account => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    if (!account.feishu_app_id) {\n      message.warning('请先绑定飞书应用后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 30); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n    setDownloadModalVisible(true);\n  };\n  const handleDownloadSubmit = async values => {\n    if (!downloadAccount) return;\n    const selectedTypes = values.data_types;\n    if (!selectedTypes || selectedTypes.length === 0) {\n      message.error('请至少选择一种数据类型');\n      return;\n    }\n    setDownloadLoading(true);\n    setDownloadProgress('正在启动批量下载任务...');\n    try {\n      // 启动批量下载任务\n      const response = await api.post(`/wechat/batch-download-data/${downloadAccount.id}`, {\n        start_date: values.start_date,\n        end_date: values.end_date,\n        data_types: selectedTypes\n      });\n      if (response.data.success && response.data.task_id) {\n        const taskId = response.data.task_id;\n        setDownloadProgress('下载任务已启动，正在监控进度...');\n\n        // 开始轮询任务状态\n        const pollInterval = setInterval(async () => {\n          try {\n            const statusResponse = await api.get(`/wechat/download-task-status/${taskId}`);\n            const taskStatus = statusResponse.data.task_status;\n            if (taskStatus.status === 'running') {\n              setDownloadProgress(`正在下载 ${taskStatus.progress}/${taskStatus.total} 个文件 (${taskStatus.current_file || '准备中'})`);\n            } else if (taskStatus.status === 'completed') {\n              clearInterval(pollInterval);\n              const {\n                downloaded_files,\n                failed_files\n              } = taskStatus;\n              if (downloaded_files.length > 0) {\n                message.success(`批量下载完成！成功: ${downloaded_files.length} 个文件${failed_files.length > 0 ? `，失败: ${failed_files.length} 个文件` : ''}`);\n              }\n              if (failed_files.length === 0) {\n                setDownloadModalVisible(false);\n              } else {\n                console.error('下载失败的文件:', failed_files);\n              }\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            } else if (taskStatus.status === 'failed') {\n              clearInterval(pollInterval);\n              message.error(`下载任务失败: ${taskStatus.message}`);\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            }\n          } catch (error) {\n            console.error('查询任务状态失败:', error);\n          }\n        }, 2000); // 每2秒查询一次状态\n\n        // 5分钟后停止轮询\n        setTimeout(() => {\n          clearInterval(pollInterval);\n          if (downloadLoading) {\n            message.warning('下载任务超时，请手动刷新查看结果');\n            setDownloadLoading(false);\n            setDownloadProgress('');\n          }\n        }, 300000);\n      } else {\n        message.error('启动下载任务失败');\n        setDownloadLoading(false);\n        setDownloadProgress('');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('启动下载任务错误:', error);\n      message.error(`启动下载任务失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n      setDownloadLoading(false);\n      setDownloadProgress('');\n    }\n  };\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    setDownloadProgress('');\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/wechat/logout-all');\n      const {\n        success,\n        message: msg,\n        logout_results\n      } = response.data;\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter(r => r.success).length;\n        const totalCount = logout_results.length;\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const loadFeishuApps = async () => {\n    try {\n      const apps = await feishuAppService.getFeishuAppsWithBitable();\n      setFeishuApps(apps);\n    } catch (error) {\n      console.error('加载飞书应用失败:', error);\n      message.error('加载飞书应用列表失败');\n    }\n  };\n  const handleBindFeishuApp = async account => {\n    setFeishuAccount(account);\n    await loadFeishuApps();\n    setFeishuBindModalVisible(true);\n  };\n  const handleConfirmBindFeishuApp = async () => {\n    if (!feishuAccount || !selectedFeishuAppId) {\n      message.error('请选择飞书应用');\n      return;\n    }\n    try {\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n      const response = await api.post(`/feishu/bind-app/${feishuAccount.id}?feishu_app_id=${selectedFeishuAppId}`);\n      if (response.data.success) {\n        message.success('飞书应用绑定成功！');\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('绑定飞书应用失败');\n      }\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('绑定飞书应用失败:', error);\n      message.error(`绑定失败: ${((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || '未知错误'}`);\n    } finally {\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n  const handleSyncToFeishu = account => {\n    if (!account.feishu_app_id) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n\n    // 显示确认对话框\n    Modal.confirm({\n      title: '确认同步数据到飞书',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6B64\\u64CD\\u4F5C\\u5C06\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            paddingLeft: '20px',\n            margin: '10px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u4ECE\\u670D\\u52A1\\u5668\\u5B58\\u50A8\\u76EE\\u5F55\\u8BFB\\u53D6\\u5DF2\\u4E0B\\u8F7D\\u7684Excel\\u6587\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5220\\u9664\\u98DE\\u4E66\\u8868\\u683C\\u4E2D\\u7684\\u5DF2\\u6709\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5C06\\u65B0\\u6570\\u636E\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\u591A\\u7EF4\\u8868\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#ff4d4f',\n            fontWeight: 'bold'\n          },\n          children: \"\\u8BF7\\u786E\\u4FDD\\u5DF2\\u5148\\u4E0B\\u8F7D\\u6570\\u636E\\u5230\\u670D\\u52A1\\u5668\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this),\n      okText: '确认同步',\n      cancelText: '取消',\n      onOk: () => {\n        // 直接开始同步，不需要显示日期选择模态框\n        handleDirectSync(account);\n      }\n    });\n  };\n  const handleDirectSync = async account => {\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(account.id));\n      const response = await api.post(`/feishu/sync-data/${account.id}`);\n      if (response.data.success) {\n        const {\n          sync_results\n        } = response.data;\n        message.success(`数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`);\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(account.id);\n        return newSet;\n      });\n    }\n  };\n  const columns = [{\n    title: '账号名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '平台类型',\n    dataIndex: 'platform',\n    key: 'platform',\n    render: platform => {\n      const option = platformOptions.find(opt => opt.value === platform);\n      return option ? option.label : platform;\n    }\n  }, {\n    title: '登录状态',\n    dataIndex: 'login_status',\n    key: 'login_status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status ? 'green' : 'red',\n      children: status ? '已登录' : '未登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后登录时间',\n    dataIndex: 'last_login_time',\n    key: 'last_login_time',\n    render: time => time ? new Date(time).toLocaleString() : '-'\n  }, {\n    title: '飞书状态',\n    key: 'feishu_status',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Tag, {\n      color: record.feishu_app_id ? 'blue' : 'default',\n      children: record.feishu_app_id ? '已创建' : '未创建'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleLogin(record),\n        disabled: record.login_status,\n        children: \"\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDownload(record),\n        disabled: !record.login_status || !record.feishu_app_id,\n        children: \"\\u4E0B\\u8F7D\\u5230\\u670D\\u52A1\\u5668\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this), !record.feishu_app_id ? /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(LinkOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleBindFeishuApp(record),\n        loading: feishuLoadingAccounts.has(record.id),\n        disabled: feishuLoadingAccounts.has(record.id),\n        children: \"\\u7ED1\\u5B9A\\u98DE\\u4E66\\u5E94\\u7528\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(CloudSyncOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: () => handleSyncToFeishu(record),\n        disabled: !record.login_status || feishuLoadingAccounts.has(record.id),\n        loading: feishuLoadingAccounts.has(record.id),\n        children: \"\\u540C\\u6B65\\u5230\\u98DE\\u4E66\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u8FD9\\u4E2A\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n        description: \"\\u6CE8\\u9500\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u626B\\u7801\\u767B\\u5F55\",\n        onConfirm: () => handleLogout(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          loading: logoutLoading === record.id,\n          disabled: !record.login_status || logoutLoading === record.id,\n          children: \"\\u6CE8\\u9500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8D26\\u53F7\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8D26\\u53F7\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u8981\\u6CE8\\u9500\\u6240\\u6709\\u8D26\\u53F7\\u7684\\u767B\\u5F55\\u72B6\\u6001\\u5417\\uFF1F\",\n          description: \"\\u8FD9\\u5C06\\u6CE8\\u9500\\u5F53\\u524D\\u7528\\u6237\\u7684\\u6240\\u6709\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u8D26\\u53F7\\u767B\\u5F55\\u72B6\\u6001\",\n          onConfirm: handleLogoutAll,\n          okText: \"\\u786E\\u5B9A\",\n          cancelText: \"\\u53D6\\u6D88\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 23\n            }, this),\n            loading: loading,\n            disabled: accounts.length === 0 || !accounts.some(acc => acc.login_status),\n            children: \"\\u6CE8\\u9500\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 21\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u6DFB\\u52A0\\u8D26\\u53F7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: accounts,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAccount ? '编辑账号' : '添加账号',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8D26\\u53F7\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入账号名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D26\\u53F7\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"platform\",\n          label: \"\\u5E73\\u53F0\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择平台类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E73\\u53F0\\u7C7B\\u578B\",\n            children: platformOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `登录 ${loginAccount === null || loginAccount === void 0 ? void 0 : loginAccount.name}`,\n      open: loginModalVisible,\n      onCancel: handleLoginModalClose,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoginModalClose,\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => loginAccount && handleLogin(loginAccount),\n        disabled: loginLoading || loginStatus === 'success',\n        children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801\"\n      }, \"retry\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 11\n      }, this)],\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px 0'\n        },\n        children: [loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16\n            },\n            children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 13\n        }, this), loginStatus === 'scanning' && qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: qrCodeUrl,\n            alt: \"\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801\",\n            width: 200,\n            height: 200,\n            style: {\n              border: '1px solid #d9d9d9'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16,\n              color: '#1890ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this), \" \\u8BF7\\u4F7F\\u7528\\u5FAE\\u4FE1\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\\u767B\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: \"\\u4E8C\\u7EF4\\u7801\\u5C06\\u572830\\u79D2\\u540E\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 13\n        }, this), loginStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#52c41a',\n              marginBottom: 16\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u6210\\u529F\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this), loginStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#ff4d4f',\n              marginBottom: 16\n            },\n            children: \"\\u2717\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 13\n        }, this), loginStatus === 'waiting' && !loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {\n            style: {\n              fontSize: '48px',\n              color: '#d9d9d9',\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u51C6\\u5907\\u83B7\\u53D6\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `下载数据 - ${downloadAccount === null || downloadAccount === void 0 ? void 0 : downloadAccount.name}`,\n      open: downloadModalVisible,\n      onCancel: handleDownloadModalClose,\n      onOk: () => downloadForm.submit(),\n      okText: \"\\u4E0B\\u8F7D\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: downloadLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: downloadForm,\n        layout: \"vertical\",\n        onFinish: handleDownloadSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"start_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"data_types\",\n          label: \"\\u9009\\u62E9\\u6570\\u636E\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请至少选择一种数据类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            style: {\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_trend\",\n                children: \"\\u5185\\u5BB9\\u6570\\u636E\\u8D8B\\u52BF\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_source\",\n                children: \"\\u5185\\u5BB9\\u6D41\\u91CF\\u6765\\u6E90\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"content_detail\",\n                children: \"\\u5185\\u5BB9\\u5DF2\\u901A\\u77E5\\u5185\\u5BB9\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"user_channel\",\n                children: \"\\u7528\\u6237\\u6E20\\u9053\\u6784\\u6210\\u660E\\u7EC6\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 11\n        }, this), downloadProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#e6f7ff',\n            padding: '12px',\n            borderRadius: '6px',\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '14px',\n              color: '#1890ff'\n            },\n            children: downloadProgress\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u53EF\\u9009\\u62E9\\u591A\\u79CD\\u6570\\u636E\\u7C7B\\u578B\\u8FDB\\u884C\\u6279\\u91CF\\u4E0B\\u8F7D\\u5230\\u670D\\u52A1\\u5668\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u6587\\u4EF6\\u5C06\\u4FDD\\u5B58\\u5728\\u670D\\u52A1\\u5668\\u5B58\\u50A8\\u76EE\\u5F55\\u4E2D\\uFF0C\\u4E0D\\u4F1A\\u4E0B\\u8F7D\\u5230\\u672C\\u5730\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u91C7\\u7528\\u5F02\\u6B65\\u4EFB\\u52A1\\u6A21\\u5F0F\\uFF0C\\u652F\\u6301\\u5B9E\\u65F6\\u8FDB\\u5EA6\\u76D1\\u63A7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u591A\\u4E2A\\u6587\\u4EF6\\u4E0B\\u8F7D\\u65F6\\u4F1A\\u81EA\\u52A8\\u95F4\\u96942-5\\u79D2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u4E14\\u5DF2\\u7ED1\\u5B9A\\u98DE\\u4E66\\u5E94\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4E0B\\u8F7D\\u5B8C\\u6210\\u540E\\u53EF\\u4F7F\\u7528\\\"\\u540C\\u6B65\\u5230\\u98DE\\u4E66\\\"\\u529F\\u80FD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `绑定飞书应用 - ${feishuAccount === null || feishuAccount === void 0 ? void 0 : feishuAccount.name}`,\n      open: feishuBindModalVisible,\n      onCancel: () => {\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n      },\n      onOk: handleConfirmBindFeishuApp,\n      okText: \"\\u786E\\u8BA4\\u7ED1\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false,\n      width: 500,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u8BF7\\u9009\\u62E9\\u8981\\u7ED1\\u5B9A\\u7684\\u98DE\\u4E66\\u5E94\\u7528\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u98DE\\u4E66\\u5E94\\u7528\",\n          value: selectedFeishuAppId,\n          onChange: setSelectedFeishuAppId,\n          showSearch: true,\n          optionFilterProp: \"children\",\n          children: feishuApps.map(app => /*#__PURE__*/_jsxDEV(Option, {\n            value: app.id,\n            children: [app.name, \" - \", app.bitable_name]\n          }, app.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f0f2f5',\n          padding: '12px',\n          borderRadius: '6px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BF4\\u660E\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '8px 0 0 0',\n            paddingLeft: '16px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u53EA\\u663E\\u793A\\u5DF2\\u521B\\u5EFA\\u591A\\u7EF4\\u8868\\u683C\\u7684\\u98DE\\u4E66\\u5E94\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u7ED1\\u5B9A\\u540E\\u5C06\\u5220\\u9664\\u8D26\\u53F7\\u539F\\u6709\\u7684\\u6570\\u636E\\u8868\\uFF0C\\u5E76\\u521B\\u5EFA\\u65B0\\u7684\\u6570\\u636E\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5982\\u9700\\u521B\\u5EFA\\u65B0\\u7684\\u98DE\\u4E66\\u5E94\\u7528\\uFF0C\\u8BF7\\u524D\\u5F80\\\"\\u98DE\\u4E66\\u5E94\\u7528\\u7BA1\\u7406\\\"\\u9875\\u9762\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 646,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountManage, \"vY9KpeUXr7u1JpQNtsLcYbvkYM0=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = AccountManage;\nexport default AccountManage;\nvar _c;\n$RefreshReg$(_c, \"AccountManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Tag", "Popconfirm", "Image", "Spin", "Checkbox", "PlusOutlined", "EditOutlined", "DeleteOutlined", "LoginOutlined", "QrcodeOutlined", "DownloadOutlined", "LogoutOutlined", "CloudSyncOutlined", "LinkOutlined", "api", "feishuAppService", "jsxDEV", "_jsxDEV", "Option", "AccountManage", "_s", "accounts", "setAccounts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingAccount", "setEditingAccount", "form", "useForm", "loginModalVisible", "setLoginModalVisible", "loginAccount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "qrCodeUrl", "setQrCodeUrl", "loginLoading", "setLoginLoading", "loginStatus", "setLoginStatus", "downloadModalVisible", "setDownloadModalVisible", "downloadAccount", "setDownloadAccount", "downloadLoading", "setDownloadLoading", "downloadProgress", "setDownloadProgress", "downloadForm", "logoutLoading", "setLogoutLoading", "feishuBindModalVisible", "setFeishuBindModalVisible", "feishuAccount", "set<PERSON><PERSON><PERSON>A<PERSON>unt", "feishuLoadingAccounts", "setFeishuLoadingAccounts", "Set", "feishuApps", "setFeishuApps", "selectedFeishuAppId", "setSelectedFeishuAppId", "platformOptions", "value", "label", "fetchAccounts", "response", "get", "data", "error", "handleAdd", "resetFields", "handleEdit", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "delete", "success", "handleSubmit", "values", "put", "post", "_error$response", "_error$response$data", "detail", "handleLogin", "qrcode", "startLoginStatusPolling", "_error$response2", "_error$response2$data", "accountId", "pollInterval", "setInterval", "logged_in", "clearInterval", "console", "setTimeout", "warning", "handleLoginModalClose", "handleDownload", "login_status", "feishu_app_id", "today", "Date", "endDate", "setDate", "getDate", "startDate", "start_date", "toISOString", "split", "end_date", "data_types", "handleDownloadSubmit", "selectedTypes", "length", "task_id", "taskId", "statusResponse", "taskStatus", "task_status", "status", "progress", "total", "current_file", "downloaded_files", "failed_files", "_error$response3", "_error$response3$data", "handleDownloadModalClose", "handleLogout", "clear_saved_state", "name", "_error$response4", "_error$response4$data", "handleForceLogout", "_error$response5", "_error$response5$data", "handleLogoutAll", "msg", "logout_results", "successCount", "filter", "r", "totalCount", "_error$response6", "_error$response6$data", "loadFeishuApps", "apps", "getFeishuAppsWithBitable", "handleBindFeishuApp", "handleConfirmBindFeishuApp", "prev", "add", "_error$response7", "_error$response7$data", "newSet", "handleSyncToFeishu", "confirm", "title", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "paddingLeft", "margin", "color", "fontWeight", "okText", "cancelText", "onOk", "handleDirectSync", "sync_results", "user_data_synced", "article_data_synced", "_error$response8", "_error$response8$data", "columns", "dataIndex", "key", "render", "platform", "option", "find", "opt", "time", "toLocaleString", "_", "record", "size", "type", "icon", "onClick", "disabled", "has", "description", "onConfirm", "danger", "extra", "some", "acc", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "open", "onCancel", "submit", "layout", "onFinish", "<PERSON><PERSON>", "rules", "required", "placeholder", "map", "footer", "width", "textAlign", "padding", "marginTop", "src", "alt", "height", "border", "fontSize", "marginBottom", "confirmLoading", "Group", "display", "flexDirection", "gap", "background", "borderRadius", "onChange", "showSearch", "optionFilterProp", "app", "bitable_name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Popconfirm,\n  Image,\n  Spin,\n  Checkbox\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, LinkOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { feishuAppService, FeishuApp } from '../services/feishuAppService';\n\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  platform: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n  feishu_table_id?: string;\n  feishu_created_at?: string;\n  feishu_app_id?: number;\n}\n\nconst AccountManage: React.FC = () => {\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState<Account | null>(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState<Account | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadProgress, setDownloadProgress] = useState<string>('');\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState<number | null>(null); // 存储正在注销的账号ID\n\n  // 飞书相关状态\n  const [feishuBindModalVisible, setFeishuBindModalVisible] = useState(false);\n  const [feishuAccount, setFeishuAccount] = useState<Account | null>(null);\n  const [feishuLoadingAccounts, setFeishuLoadingAccounts] = useState<Set<number>>(new Set());\n  const [feishuApps, setFeishuApps] = useState<FeishuApp[]>([]);\n  const [selectedFeishuAppId, setSelectedFeishuAppId] = useState<number | null>(null);\n\n  const platformOptions = [\n    { value: 'wechat_mp', label: '微信公众号' },\n    { value: 'wechat_service', label: '微信服务号' },\n    { value: 'wechat_channels', label: '视频号' },\n    { value: 'xiaohongshu', label: '小红书' },\n  ];\n\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (account: Account) => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleLogin = async (account: Account) => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n\n  const startLoginStatusPolling = (accountId: number) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const { logged_in } = response.data;\n\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = (account: Account) => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    if (!account.feishu_app_id) {\n      message.warning('请先绑定飞书应用后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 30); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      data_types: ['content_trend', 'content_source', 'content_detail', 'user_channel'] // 默认全选\n    });\n\n    setDownloadModalVisible(true);\n  };\n\n  const handleDownloadSubmit = async (values: any) => {\n    if (!downloadAccount) return;\n\n    const selectedTypes = values.data_types;\n    if (!selectedTypes || selectedTypes.length === 0) {\n      message.error('请至少选择一种数据类型');\n      return;\n    }\n\n    setDownloadLoading(true);\n    setDownloadProgress('正在启动批量下载任务...');\n\n    try {\n      // 启动批量下载任务\n      const response = await api.post(`/wechat/batch-download-data/${downloadAccount.id}`, {\n        start_date: values.start_date,\n        end_date: values.end_date,\n        data_types: selectedTypes\n      });\n\n      if (response.data.success && response.data.task_id) {\n        const taskId = response.data.task_id;\n        setDownloadProgress('下载任务已启动，正在监控进度...');\n\n        // 开始轮询任务状态\n        const pollInterval = setInterval(async () => {\n          try {\n            const statusResponse = await api.get(`/wechat/download-task-status/${taskId}`);\n            const taskStatus = statusResponse.data.task_status;\n\n            if (taskStatus.status === 'running') {\n              setDownloadProgress(\n                `正在下载 ${taskStatus.progress}/${taskStatus.total} 个文件 (${taskStatus.current_file || '准备中'})`\n              );\n            } else if (taskStatus.status === 'completed') {\n              clearInterval(pollInterval);\n              const { downloaded_files, failed_files } = taskStatus;\n\n              if (downloaded_files.length > 0) {\n                message.success(`批量下载完成！成功: ${downloaded_files.length} 个文件${failed_files.length > 0 ? `，失败: ${failed_files.length} 个文件` : ''}`);\n              }\n\n              if (failed_files.length === 0) {\n                setDownloadModalVisible(false);\n              } else {\n                console.error('下载失败的文件:', failed_files);\n              }\n\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            } else if (taskStatus.status === 'failed') {\n              clearInterval(pollInterval);\n              message.error(`下载任务失败: ${taskStatus.message}`);\n              setDownloadLoading(false);\n              setDownloadProgress('');\n            }\n          } catch (error) {\n            console.error('查询任务状态失败:', error);\n          }\n        }, 2000); // 每2秒查询一次状态\n\n        // 5分钟后停止轮询\n        setTimeout(() => {\n          clearInterval(pollInterval);\n          if (downloadLoading) {\n            message.warning('下载任务超时，请手动刷新查看结果');\n            setDownloadLoading(false);\n            setDownloadProgress('');\n          }\n        }, 300000);\n\n      } else {\n        message.error('启动下载任务失败');\n        setDownloadLoading(false);\n        setDownloadProgress('');\n      }\n    } catch (error: any) {\n      console.error('启动下载任务错误:', error);\n      message.error(`启动下载任务失败: ${error.response?.data?.detail || error.message}`);\n      setDownloadLoading(false);\n      setDownloadProgress('');\n    }\n  };\n\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    setDownloadProgress('');\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error: any) {\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/wechat/force-logout/${account.id}`);\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n      \n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n\n      const response = await api.get('/wechat/logout-all');\n\n      const { success, message: msg, logout_results } = response.data;\n\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter((r: any) => r.success).length;\n        const totalCount = logout_results.length;\n\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 飞书相关处理函数\n  const loadFeishuApps = async () => {\n    try {\n      const apps = await feishuAppService.getFeishuAppsWithBitable();\n      setFeishuApps(apps);\n    } catch (error: any) {\n      console.error('加载飞书应用失败:', error);\n      message.error('加载飞书应用列表失败');\n    }\n  };\n\n  const handleBindFeishuApp = async (account: Account) => {\n    setFeishuAccount(account);\n    await loadFeishuApps();\n    setFeishuBindModalVisible(true);\n  };\n\n  const handleConfirmBindFeishuApp = async () => {\n    if (!feishuAccount || !selectedFeishuAppId) {\n      message.error('请选择飞书应用');\n      return;\n    }\n\n    try {\n      setFeishuLoadingAccounts(prev => new Set(prev).add(feishuAccount.id));\n\n      const response = await api.post(`/feishu/bind-app/${feishuAccount.id}?feishu_app_id=${selectedFeishuAppId}`);\n\n      if (response.data.success) {\n        message.success('飞书应用绑定成功！');\n        setFeishuBindModalVisible(false);\n        setFeishuAccount(null);\n        setSelectedFeishuAppId(null);\n        fetchAccounts(); // 刷新账号列表\n      } else {\n        message.error('绑定飞书应用失败');\n      }\n    } catch (error: any) {\n      console.error('绑定飞书应用失败:', error);\n      message.error(`绑定失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(feishuAccount.id);\n        return newSet;\n      });\n    }\n  };\n\n  const handleSyncToFeishu = (account: Account) => {\n    if (!account.feishu_app_id) {\n      message.warning('请先创建飞书多维表格');\n      return;\n    }\n    if (!account.login_status) {\n      message.warning('请先登录微信公众号');\n      return;\n    }\n\n    // 显示确认对话框\n    Modal.confirm({\n      title: '确认同步数据到飞书',\n      content: (\n        <div>\n          <p>此操作将：</p>\n          <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>\n            <li>从服务器存储目录读取已下载的Excel文件</li>\n            <li>删除飞书表格中的已有数据</li>\n            <li>将新数据同步到飞书多维表格</li>\n          </ul>\n          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>\n            请确保已先下载数据到服务器！\n          </p>\n        </div>\n      ),\n      okText: '确认同步',\n      cancelText: '取消',\n      onOk: () => {\n        // 直接开始同步，不需要显示日期选择模态框\n        handleDirectSync(account);\n      }\n    });\n  };\n\n  const handleDirectSync = async (account: Account) => {\n    try {\n      // 添加当前账号到loading状态\n      setFeishuLoadingAccounts(prev => new Set(prev).add(account.id));\n\n      const response = await api.post(`/feishu/sync-data/${account.id}`);\n\n      if (response.data.success) {\n        const { sync_results } = response.data;\n        message.success(\n          `数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`\n        );\n      } else {\n        message.error('数据同步失败');\n      }\n    } catch (error: any) {\n      console.error('同步数据失败:', error);\n      message.error(`同步失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      // 从loading状态中移除当前账号\n      setFeishuLoadingAccounts(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(account.id);\n        return newSet;\n      });\n    }\n  };\n\n\n\n  const columns = [\n    {\n      title: '账号名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '平台类型',\n      dataIndex: 'platform',\n      key: 'platform',\n      render: (platform: string) => {\n        const option = platformOptions.find(opt => opt.value === platform);\n        return option ? option.label : platform;\n      },\n    },\n    {\n      title: '登录状态',\n      dataIndex: 'login_status',\n      key: 'login_status',\n      render: (status: boolean) => (\n        <Tag color={status ? 'green' : 'red'}>\n          {status ? '已登录' : '未登录'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'last_login_time',\n      key: 'last_login_time',\n      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',\n    },\n    {\n      title: '飞书状态',\n      key: 'feishu_status',\n      render: (_: any, record: Account) => (\n        <Tag color={record.feishu_app_id ? 'blue' : 'default'}>\n          {record.feishu_app_id ? '已创建' : '未创建'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: Account) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"primary\" \n            icon={<LoginOutlined />} \n            size=\"small\"\n            onClick={() => handleLogin(record)}\n            disabled={record.login_status}\n          >\n            登录\n          </Button>\n          <Button\n            type=\"default\"\n            icon={<DownloadOutlined />}\n            size=\"small\"\n            onClick={() => handleDownload(record)}\n            disabled={!record.login_status || !record.feishu_app_id}\n          >\n            下载到服务器\n          </Button>\n          {!record.feishu_app_id ? (\n            <Button\n              type=\"default\"\n              icon={<LinkOutlined />}\n              size=\"small\"\n              onClick={() => handleBindFeishuApp(record)}\n              loading={feishuLoadingAccounts.has(record.id)}\n              disabled={feishuLoadingAccounts.has(record.id)}\n            >\n              绑定飞书应用\n            </Button>\n          ) : (\n            <Button\n              type=\"default\"\n              icon={<CloudSyncOutlined />}\n              size=\"small\"\n              onClick={() => handleSyncToFeishu(record)}\n              disabled={!record.login_status || feishuLoadingAccounts.has(record.id)}\n              loading={feishuLoadingAccounts.has(record.id)}\n            >\n              同步到飞书\n            </Button>\n          )}\n          <Popconfirm\n            title=\"确定要注销这个账号的登录状态吗？\"\n            description=\"注销后需要重新扫码登录\"\n            onConfirm={() => handleLogout(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"default\"\n              icon={<LogoutOutlined />} \n              size=\"small\"\n              loading={logoutLoading === record.id}\n              disabled={!record.login_status || logoutLoading === record.id}\n            >\n              注销\n            </Button>\n          </Popconfirm>\n          <Button \n            icon={<EditOutlined />} \n            size=\"small\"\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个账号吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              danger \n              icon={<DeleteOutlined />} \n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card \n        title=\"账号管理\" \n        extra={\n          <Space>\n            <Popconfirm\n              title=\"确定要注销所有账号的登录状态吗？\"\n              description=\"这将注销当前用户的所有微信公众号账号登录状态\"\n              onConfirm={handleLogoutAll}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button \n                type=\"default\"\n                icon={<LogoutOutlined />}\n                loading={loading}\n                disabled={accounts.length === 0 || !accounts.some(acc => acc.login_status)}\n              >\n                注销全部\n              </Button>\n            </Popconfirm>\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加账号\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={accounts}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAccount ? '编辑账号' : '添加账号'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"账号名称\"\n            rules={[{ required: true, message: '请输入账号名称' }]}\n          >\n            <Input placeholder=\"请输入账号名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"platform\"\n            label=\"平台类型\"\n            rules={[{ required: true, message: '请选择平台类型' }]}\n          >\n            <Select placeholder=\"请选择平台类型\">\n              {platformOptions.map(option => (\n                <Option key={option.value} value={option.value}>\n                  {option.label}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 登录模态框 */}\n      <Modal\n        title={`登录 ${loginAccount?.name}`}\n        open={loginModalVisible}\n        onCancel={handleLoginModalClose}\n        footer={[\n          <Button key=\"close\" onClick={handleLoginModalClose}>\n            关闭\n          </Button>,\n          <Button\n            key=\"retry\"\n            type=\"primary\"\n            onClick={() => loginAccount && handleLogin(loginAccount)}\n            disabled={loginLoading || loginStatus === 'success'}\n          >\n            重新获取二维码\n          </Button>\n        ]}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          {loginLoading && (\n            <div>\n              <Spin size=\"large\" />\n              <p style={{ marginTop: 16 }}>正在获取二维码...</p>\n            </div>\n          )}\n\n          {loginStatus === 'scanning' && qrCodeUrl && (\n            <div>\n              <Image\n                src={qrCodeUrl}\n                alt=\"登录二维码\"\n                width={200}\n                height={200}\n                style={{ border: '1px solid #d9d9d9' }}\n              />\n              <p style={{ marginTop: 16, color: '#1890ff' }}>\n                <QrcodeOutlined /> 请使用微信扫描二维码登录\n              </p>\n              <p style={{ color: '#666', fontSize: '12px' }}>\n                二维码将在30秒后过期\n              </p>\n            </div>\n          )}\n\n          {loginStatus === 'success' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>\n                ✓\n              </div>\n              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>\n            </div>\n          )}\n\n          {loginStatus === 'failed' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>\n                ✗\n              </div>\n              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>\n            </div>\n          )}\n\n          {loginStatus === 'waiting' && !loginLoading && (\n            <div>\n              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />\n              <p style={{ color: '#666' }}>准备获取登录二维码...</p>\n            </div>\n          )}\n        </div>\n      </Modal>\n\n      {/* 下载数据模态框 */}\n      <Modal\n        title={`下载数据 - ${downloadAccount?.name}`}\n        open={downloadModalVisible}\n        onCancel={handleDownloadModalClose}\n        onOk={() => downloadForm.submit()}\n        okText=\"下载\"\n        cancelText=\"取消\"\n        confirmLoading={downloadLoading}\n        width={500}\n      >\n        <Form\n          form={downloadForm}\n          layout=\"vertical\"\n          onFinish={handleDownloadSubmit}\n        >\n          <Form.Item\n            name=\"start_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"data_types\"\n            label=\"选择数据类型\"\n            rules={[{ required: true, message: '请至少选择一种数据类型' }]}\n          >\n            <Checkbox.Group style={{ width: '100%' }}>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n                <Checkbox value=\"content_trend\">内容数据趋势明细表</Checkbox>\n                <Checkbox value=\"content_source\">内容流量来源明细表</Checkbox>\n                <Checkbox value=\"content_detail\">内容已通知内容明细表</Checkbox>\n                <Checkbox value=\"user_channel\">用户渠道构成明细表</Checkbox>\n              </div>\n            </Checkbox.Group>\n          </Form.Item>\n\n          {downloadProgress && (\n            <div style={{ background: '#e6f7ff', padding: '12px', borderRadius: '6px', marginBottom: '16px' }}>\n              <p style={{ margin: 0, fontSize: '14px', color: '#1890ff' }}>\n                {downloadProgress}\n              </p>\n            </div>\n          )}\n          \n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>可选择多种数据类型进行批量下载到服务器</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>文件将保存在服务器存储目录中，不会下载到本地</li>\n              <li>采用异步任务模式，支持实时进度监控</li>\n              <li>多个文件下载时会自动间隔2-5秒</li>\n              <li>请确保账号已登录且已绑定飞书应用</li>\n              <li>下载完成后可使用\"同步到飞书\"功能</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n\n\n\n      {/* 绑定飞书应用模态框 */}\n      <Modal\n        title={`绑定飞书应用 - ${feishuAccount?.name}`}\n        open={feishuBindModalVisible}\n        onCancel={() => {\n          setFeishuBindModalVisible(false);\n          setFeishuAccount(null);\n          setSelectedFeishuAppId(null);\n        }}\n        onOk={handleConfirmBindFeishuApp}\n        okText=\"确认绑定\"\n        cancelText=\"取消\"\n        confirmLoading={feishuAccount ? feishuLoadingAccounts.has(feishuAccount.id) : false}\n        width={500}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <p>请选择要绑定的飞书应用：</p>\n          <Select\n            style={{ width: '100%' }}\n            placeholder=\"请选择飞书应用\"\n            value={selectedFeishuAppId}\n            onChange={setSelectedFeishuAppId}\n            showSearch\n            optionFilterProp=\"children\"\n          >\n            {feishuApps.map(app => (\n              <Option key={app.id} value={app.id}>\n                {app.name} - {app.bitable_name}\n              </Option>\n            ))}\n          </Select>\n        </div>\n\n        <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px' }}>\n          <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n            <strong>说明：</strong>\n          </p>\n          <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n            <li>只显示已创建多维表格的飞书应用</li>\n            <li>绑定后将删除账号原有的数据表，并创建新的数据表</li>\n            <li>如需创建新的飞书应用，请前往\"飞书应用管理\"页面</li>\n          </ul>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AccountManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,mBAAmB;AAChL,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAASC,gBAAgB,QAAmB,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAM;EAAEC;AAAO,CAAC,GAAGrB,MAAM;AAczB,MAAMsB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACwC,IAAI,CAAC,GAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAiB,IAAI,CAAC;EACtE,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAS,SAAS,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACoD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC4D,YAAY,CAAC,GAAGtD,IAAI,CAACmC,OAAO,CAAC,CAAC;;EAErC;EACA,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAAC+D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAiB,IAAI,CAAC;EACxE,MAAM,CAACmE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpE,QAAQ,CAAc,IAAIqE,GAAG,CAAC,CAAC,CAAC;EAC1F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAgB,IAAI,CAAC;EAEnF,MAAM0E,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAM,CAAC,CACvC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,YAAY,CAAC;MAC5C9C,WAAW,CAAC6C,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACd4E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB3C,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAAC2C,WAAW,CAAC,CAAC;IAClB9C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+C,UAAU,GAAIC,OAAgB,IAAK;IACvC9C,iBAAiB,CAAC8C,OAAO,CAAC;IAC1B7C,IAAI,CAAC8C,cAAc,CAACD,OAAO,CAAC;IAC5BhD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkD,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM/D,GAAG,CAACgE,MAAM,CAAC,aAAaD,EAAE,EAAE,CAAC;MACnC/E,OAAO,CAACiF,OAAO,CAAC,MAAM,CAAC;MACvBb,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAItD,cAAc,EAAE;QAClB,MAAMb,GAAG,CAACoE,GAAG,CAAC,aAAavD,cAAc,CAACkD,EAAE,EAAE,EAAEI,MAAM,CAAC;QACvDnF,OAAO,CAACiF,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMjE,GAAG,CAACqE,IAAI,CAAC,YAAY,EAAEF,MAAM,CAAC;QACpCnF,OAAO,CAACiF,OAAO,CAAC,MAAM,CAAC;MACzB;MACArD,eAAe,CAAC,KAAK,CAAC;MACtBwC,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnBvF,OAAO,CAACwE,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOb,OAAgB,IAAK;IAC9CxC,eAAe,CAACwC,OAAO,CAAC;IACxB1C,oBAAoB,CAAC,IAAI,CAAC;IAC1BQ,cAAc,CAAC,SAAS,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM6B,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MACrE,IAAIV,QAAQ,CAACE,IAAI,CAACmB,MAAM,EAAE;QACxBpD,YAAY,CAAC+B,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC;QAClChD,cAAc,CAAC,UAAU,CAAC;;QAE1B;QACAiD,uBAAuB,CAACf,OAAO,CAACG,EAAE,CAAC;MACrC,CAAC,MAAM;QACL/E,OAAO,CAACwE,KAAK,CAAC,SAAS,CAAC;QACxB9B,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAO8B,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB7F,OAAO,CAACwE,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,SAAS,CAAC;MACxD9C,cAAc,CAAC,QAAQ,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMmD,uBAAuB,GAAIG,SAAiB,IAAK;IACrD,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,wBAAwBwB,SAAS,EAAE,CAAC;QACnE,MAAM;UAAEG;QAAU,CAAC,GAAG5B,QAAQ,CAACE,IAAI;QAEnC,IAAI0B,SAAS,EAAE;UACbvD,cAAc,CAAC,SAAS,CAAC;UACzB1C,OAAO,CAACiF,OAAO,CAAC,OAAO,CAAC;UACxBiB,aAAa,CAACH,YAAY,CAAC;UAC3B7D,oBAAoB,CAAC,KAAK,CAAC;UAC3BkC,aAAa,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA4B,UAAU,CAAC,MAAM;MACfF,aAAa,CAACH,YAAY,CAAC;MAC3B,IAAItD,WAAW,KAAK,UAAU,EAAE;QAC9BC,cAAc,CAAC,QAAQ,CAAC;QACxB1C,OAAO,CAACqG,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClCpE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM6D,cAAc,GAAI3B,OAAgB,IAAK;IAC3C,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;MACzBxG,OAAO,CAACqG,OAAO,CAAC,cAAc,CAAC;MAC/B;IACF;IACA,IAAI,CAACzB,OAAO,CAAC6B,aAAa,EAAE;MAC1BzG,OAAO,CAACqG,OAAO,CAAC,gBAAgB,CAAC;MACjC;IACF;IACAvD,kBAAkB,CAAC8B,OAAO,CAAC;;IAE3B;IACA,MAAM8B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAC/BE,OAAO,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEtC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACC,OAAO,CAAC;IACnCG,SAAS,CAACF,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;IAE3C3D,YAAY,CAAC0B,cAAc,CAAC;MAC1BmC,UAAU,EAAED,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAEP,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CE,UAAU,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACpF,CAAC,CAAC;IAEFxE,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMyE,oBAAoB,GAAG,MAAOlC,MAAW,IAAK;IAClD,IAAI,CAACtC,eAAe,EAAE;IAEtB,MAAMyE,aAAa,GAAGnC,MAAM,CAACiC,UAAU;IACvC,IAAI,CAACE,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MAChDvH,OAAO,CAACwE,KAAK,CAAC,aAAa,CAAC;MAC5B;IACF;IAEAxB,kBAAkB,CAAC,IAAI,CAAC;IACxBE,mBAAmB,CAAC,eAAe,CAAC;IAEpC,IAAI;MACF;MACA,MAAMmB,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,+BAA+BxC,eAAe,CAACkC,EAAE,EAAE,EAAE;QACnFiC,UAAU,EAAE7B,MAAM,CAAC6B,UAAU;QAC7BG,QAAQ,EAAEhC,MAAM,CAACgC,QAAQ;QACzBC,UAAU,EAAEE;MACd,CAAC,CAAC;MAEF,IAAIjD,QAAQ,CAACE,IAAI,CAACU,OAAO,IAAIZ,QAAQ,CAACE,IAAI,CAACiD,OAAO,EAAE;QAClD,MAAMC,MAAM,GAAGpD,QAAQ,CAACE,IAAI,CAACiD,OAAO;QACpCtE,mBAAmB,CAAC,mBAAmB,CAAC;;QAExC;QACA,MAAM6C,YAAY,GAAGC,WAAW,CAAC,YAAY;UAC3C,IAAI;YACF,MAAM0B,cAAc,GAAG,MAAM1G,GAAG,CAACsD,GAAG,CAAC,gCAAgCmD,MAAM,EAAE,CAAC;YAC9E,MAAME,UAAU,GAAGD,cAAc,CAACnD,IAAI,CAACqD,WAAW;YAElD,IAAID,UAAU,CAACE,MAAM,KAAK,SAAS,EAAE;cACnC3E,mBAAmB,CACjB,QAAQyE,UAAU,CAACG,QAAQ,IAAIH,UAAU,CAACI,KAAK,SAASJ,UAAU,CAACK,YAAY,IAAI,KAAK,GAC1F,CAAC;YACH,CAAC,MAAM,IAAIL,UAAU,CAACE,MAAM,KAAK,WAAW,EAAE;cAC5C3B,aAAa,CAACH,YAAY,CAAC;cAC3B,MAAM;gBAAEkC,gBAAgB;gBAAEC;cAAa,CAAC,GAAGP,UAAU;cAErD,IAAIM,gBAAgB,CAACV,MAAM,GAAG,CAAC,EAAE;gBAC/BvH,OAAO,CAACiF,OAAO,CAAC,cAAcgD,gBAAgB,CAACV,MAAM,OAAOW,YAAY,CAACX,MAAM,GAAG,CAAC,GAAG,QAAQW,YAAY,CAACX,MAAM,MAAM,GAAG,EAAE,EAAE,CAAC;cACjI;cAEA,IAAIW,YAAY,CAACX,MAAM,KAAK,CAAC,EAAE;gBAC7B3E,uBAAuB,CAAC,KAAK,CAAC;cAChC,CAAC,MAAM;gBACLuD,OAAO,CAAC3B,KAAK,CAAC,UAAU,EAAE0D,YAAY,CAAC;cACzC;cAEAlF,kBAAkB,CAAC,KAAK,CAAC;cACzBE,mBAAmB,CAAC,EAAE,CAAC;YACzB,CAAC,MAAM,IAAIyE,UAAU,CAACE,MAAM,KAAK,QAAQ,EAAE;cACzC3B,aAAa,CAACH,YAAY,CAAC;cAC3B/F,OAAO,CAACwE,KAAK,CAAC,WAAWmD,UAAU,CAAC3H,OAAO,EAAE,CAAC;cAC9CgD,kBAAkB,CAAC,KAAK,CAAC;cACzBE,mBAAmB,CAAC,EAAE,CAAC;YACzB;UACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;YACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACnC;QACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEV;QACA4B,UAAU,CAAC,MAAM;UACfF,aAAa,CAACH,YAAY,CAAC;UAC3B,IAAIhD,eAAe,EAAE;YACnB/C,OAAO,CAACqG,OAAO,CAAC,kBAAkB,CAAC;YACnCrD,kBAAkB,CAAC,KAAK,CAAC;YACzBE,mBAAmB,CAAC,EAAE,CAAC;UACzB;QACF,CAAC,EAAE,MAAM,CAAC;MAEZ,CAAC,MAAM;QACLlD,OAAO,CAACwE,KAAK,CAAC,UAAU,CAAC;QACzBxB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOsB,KAAU,EAAE;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MACnBjC,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxE,OAAO,CAACwE,KAAK,CAAC,aAAa,EAAA2D,gBAAA,GAAA3D,KAAK,CAACH,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsB5C,MAAM,KAAIhB,KAAK,CAACxE,OAAO,EAAE,CAAC;MAC3EgD,kBAAkB,CAAC,KAAK,CAAC;MACzBE,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMmF,wBAAwB,GAAGA,CAAA,KAAM;IACrCzF,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;IACxBI,mBAAmB,CAAC,EAAE,CAAC;IACvBC,YAAY,CAACuB,WAAW,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAG,MAAO1D,OAAgB,IAAK;IAC/C,IAAI;MACFvB,gBAAgB,CAACuB,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,kBAAkBT,OAAO,CAACG,EAAE,EAAE,EAAE;QAC9DwD,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIlE,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBjF,OAAO,CAACiF,OAAO,CAAC,MAAML,OAAO,CAAC4D,IAAI,OAAO,CAAC;QAC1C;QACApE,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLpE,OAAO,CAACqG,OAAO,CAAC,MAAMzB,OAAO,CAAC4D,IAAI,iBAAiB,CAAC;QACpD;QACApE,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAiE,gBAAA,EAAAC,qBAAA;MACnBvC,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BxE,OAAO,CAACwE,KAAK,CAAC,SAAS,EAAAiE,gBAAA,GAAAjE,KAAK,CAACH,QAAQ,cAAAoE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlE,IAAI,cAAAmE,qBAAA,uBAApBA,qBAAA,CAAsBlD,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACRnC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,iBAAiB,GAAG,MAAO/D,OAAgB,IAAK;IACpD,IAAI;MACFvB,gBAAgB,CAACuB,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MAErE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBjF,OAAO,CAACiF,OAAO,CAAC,MAAML,OAAO,CAAC4D,IAAI,SAAS,CAAC;MAC9C,CAAC,MAAM;QACLxI,OAAO,CAACqG,OAAO,CAAC,MAAMzB,OAAO,CAAC4D,IAAI,SAAS,CAAC;MAC9C;;MAEA;MACApE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAoE,gBAAA,EAAAC,qBAAA;MACnB1C,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxE,OAAO,CAACwE,KAAK,CAAC,WAAW,EAAAoE,gBAAA,GAAApE,KAAK,CAACH,QAAQ,cAAAuE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrE,IAAI,cAAAsE,qBAAA,uBAApBA,qBAAA,CAAsBrD,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRnC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFpH,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM2C,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,oBAAoB,CAAC;MAEpD,MAAM;QAAEW,OAAO;QAAEjF,OAAO,EAAE+I,GAAG;QAAEC;MAAe,CAAC,GAAG3E,QAAQ,CAACE,IAAI;MAE/D,IAAIU,OAAO,EAAE;QACXjF,OAAO,CAACiF,OAAO,CAAC8D,GAAG,CAAC;MACtB,CAAC,MAAM;QACL/I,OAAO,CAACqG,OAAO,CAAC0C,GAAG,CAAC;MACtB;;MAEA;MACA,IAAIC,cAAc,IAAIA,cAAc,CAACzB,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAM0B,YAAY,GAAGD,cAAc,CAACE,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAClE,OAAO,CAAC,CAACsC,MAAM;QACxE,MAAM6B,UAAU,GAAGJ,cAAc,CAACzB,MAAM;QAExC,IAAI0B,YAAY,KAAKG,UAAU,EAAE;UAC/BpJ,OAAO,CAACiF,OAAO,CAAC,MAAMmE,UAAU,UAAU,CAAC;QAC7C,CAAC,MAAM;UACLpJ,OAAO,CAACqG,OAAO,CAAC,GAAG4C,YAAY,IAAIG,UAAU,UAAU,CAAC;QAC1D;MACF;;MAEA;MACAhF,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAA6E,gBAAA,EAAAC,qBAAA;MACnBnD,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxE,OAAO,CAACwE,KAAK,CAAC,WAAW,EAAA6E,gBAAA,GAAA7E,KAAK,CAACH,QAAQ,cAAAgF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9E,IAAI,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsB9D,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACR9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6H,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMvI,gBAAgB,CAACwI,wBAAwB,CAAC,CAAC;MAC9D3F,aAAa,CAAC0F,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOhF,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxE,OAAO,CAACwE,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,MAAMkF,mBAAmB,GAAG,MAAO9E,OAAgB,IAAK;IACtDnB,gBAAgB,CAACmB,OAAO,CAAC;IACzB,MAAM2E,cAAc,CAAC,CAAC;IACtBhG,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMoG,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAACnG,aAAa,IAAI,CAACO,mBAAmB,EAAE;MAC1C/D,OAAO,CAACwE,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI;MACFb,wBAAwB,CAACiG,IAAI,IAAI,IAAIhG,GAAG,CAACgG,IAAI,CAAC,CAACC,GAAG,CAACrG,aAAa,CAACuB,EAAE,CAAC,CAAC;MAErE,MAAMV,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,oBAAoB7B,aAAa,CAACuB,EAAE,kBAAkBhB,mBAAmB,EAAE,CAAC;MAE5G,IAAIM,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBjF,OAAO,CAACiF,OAAO,CAAC,WAAW,CAAC;QAC5B1B,yBAAyB,CAAC,KAAK,CAAC;QAChCE,gBAAgB,CAAC,IAAI,CAAC;QACtBO,sBAAsB,CAAC,IAAI,CAAC;QAC5BI,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLpE,OAAO,CAACwE,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAsF,gBAAA,EAAAC,qBAAA;MACnB5D,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxE,OAAO,CAACwE,KAAK,CAAC,SAAS,EAAAsF,gBAAA,GAAAtF,KAAK,CAACH,QAAQ,cAAAyF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvF,IAAI,cAAAwF,qBAAA,uBAApBA,qBAAA,CAAsBvE,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR7B,wBAAwB,CAACiG,IAAI,IAAI;QAC/B,MAAMI,MAAM,GAAG,IAAIpG,GAAG,CAACgG,IAAI,CAAC;QAC5BI,MAAM,CAAChF,MAAM,CAACxB,aAAa,CAACuB,EAAE,CAAC;QAC/B,OAAOiF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIrF,OAAgB,IAAK;IAC/C,IAAI,CAACA,OAAO,CAAC6B,aAAa,EAAE;MAC1BzG,OAAO,CAACqG,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACA,IAAI,CAACzB,OAAO,CAAC4B,YAAY,EAAE;MACzBxG,OAAO,CAACqG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;;IAEA;IACAzG,KAAK,CAACsK,OAAO,CAAC;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,eACLjJ,OAAA;QAAAkJ,QAAA,gBACElJ,OAAA;UAAAkJ,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACZtJ,OAAA;UAAIuJ,KAAK,EAAE;YAAEC,WAAW,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACnDlJ,OAAA;YAAAkJ,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BtJ,OAAA;YAAAkJ,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBtJ,OAAA;YAAAkJ,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACLtJ,OAAA;UAAGuJ,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDM,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEA,CAAA,KAAM;QACV;QACAC,gBAAgB,CAACtG,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsG,gBAAgB,GAAG,MAAOtG,OAAgB,IAAK;IACnD,IAAI;MACF;MACAjB,wBAAwB,CAACiG,IAAI,IAAI,IAAIhG,GAAG,CAACgG,IAAI,CAAC,CAACC,GAAG,CAACjF,OAAO,CAACG,EAAE,CAAC,CAAC;MAE/D,MAAMV,QAAQ,GAAG,MAAMrD,GAAG,CAACqE,IAAI,CAAC,qBAAqBT,OAAO,CAACG,EAAE,EAAE,CAAC;MAElE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB,MAAM;UAAEkG;QAAa,CAAC,GAAG9G,QAAQ,CAACE,IAAI;QACtCvE,OAAO,CAACiF,OAAO,CACb,gBAAgBkG,YAAY,CAACC,gBAAgB,YAAYD,YAAY,CAACE,mBAAmB,IAC3F,CAAC;MACH,CAAC,MAAM;QACLrL,OAAO,CAACwE,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAA8G,gBAAA,EAAAC,qBAAA;MACnBpF,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxE,OAAO,CAACwE,KAAK,CAAC,SAAS,EAAA8G,gBAAA,GAAA9G,KAAK,CAACH,QAAQ,cAAAiH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/G,IAAI,cAAAgH,qBAAA,uBAApBA,qBAAA,CAAsB/F,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACR;MACA7B,wBAAwB,CAACiG,IAAI,IAAI;QAC/B,MAAMI,MAAM,GAAG,IAAIpG,GAAG,CAACgG,IAAI,CAAC;QAC5BI,MAAM,CAAChF,MAAM,CAACJ,OAAO,CAACG,EAAE,CAAC;QACzB,OAAOiF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMwB,OAAO,GAAG,CACd;IACErB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEvB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,QAAgB,IAAK;MAC5B,MAAMC,MAAM,GAAG5H,eAAe,CAAC6H,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7H,KAAK,KAAK0H,QAAQ,CAAC;MAClE,OAAOC,MAAM,GAAGA,MAAM,CAAC1H,KAAK,GAAGyH,QAAQ;IACzC;EACF,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAG9D,MAAe,iBACtB1G,OAAA,CAACjB,GAAG;MAAC2K,KAAK,EAAEhD,MAAM,GAAG,OAAO,GAAG,KAAM;MAAAwC,QAAA,EAClCxC,MAAM,GAAG,KAAK,GAAG;IAAK;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfsB,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAGK,IAAmB,IAAKA,IAAI,GAAG,IAAIrF,IAAI,CAACqF,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC5E,CAAC,EACD;IACE9B,KAAK,EAAE,MAAM;IACbuB,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACO,CAAM,EAAEC,MAAe,kBAC9BhL,OAAA,CAACjB,GAAG;MAAC2K,KAAK,EAAEsB,MAAM,CAAC1F,aAAa,GAAG,MAAM,GAAG,SAAU;MAAA4D,QAAA,EACnD8B,MAAM,CAAC1F,aAAa,GAAG,KAAK,GAAG;IAAK;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAET,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGK,IAAY,IAAK,IAAIrF,IAAI,CAACqF,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXuB,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACO,CAAM,EAAEC,MAAe,kBAC9BhL,OAAA,CAAClB,KAAK;MAACmM,IAAI,EAAC,QAAQ;MAAA/B,QAAA,gBAClBlJ,OAAA,CAACxB,MAAM;QACL0M,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEnL,OAAA,CAACT,aAAa;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxB2B,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM9G,WAAW,CAAC0G,MAAM,CAAE;QACnCK,QAAQ,EAAEL,MAAM,CAAC3F,YAAa;QAAA6D,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtJ,OAAA,CAACxB,MAAM;QACL0M,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEnL,OAAA,CAACP,gBAAgB;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3B2B,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMhG,cAAc,CAAC4F,MAAM,CAAE;QACtCK,QAAQ,EAAE,CAACL,MAAM,CAAC3F,YAAY,IAAI,CAAC2F,MAAM,CAAC1F,aAAc;QAAA4D,QAAA,EACzD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAAC0B,MAAM,CAAC1F,aAAa,gBACpBtF,OAAA,CAACxB,MAAM;QACL0M,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEnL,OAAA,CAACJ,YAAY;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvB2B,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAACyC,MAAM,CAAE;QAC3C1K,OAAO,EAAEiC,qBAAqB,CAAC+I,GAAG,CAACN,MAAM,CAACpH,EAAE,CAAE;QAC9CyH,QAAQ,EAAE9I,qBAAqB,CAAC+I,GAAG,CAACN,MAAM,CAACpH,EAAE,CAAE;QAAAsF,QAAA,EAChD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETtJ,OAAA,CAACxB,MAAM;QACL0M,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEnL,OAAA,CAACL,iBAAiB;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5B2B,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACkC,MAAM,CAAE;QAC1CK,QAAQ,EAAE,CAACL,MAAM,CAAC3F,YAAY,IAAI9C,qBAAqB,CAAC+I,GAAG,CAACN,MAAM,CAACpH,EAAE,CAAE;QACvEtD,OAAO,EAAEiC,qBAAqB,CAAC+I,GAAG,CAACN,MAAM,CAACpH,EAAE,CAAE;QAAAsF,QAAA,EAC/C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACDtJ,OAAA,CAAChB,UAAU;QACTgK,KAAK,EAAC,kGAAkB;QACxBuC,WAAW,EAAC,oEAAa;QACzBC,SAAS,EAAEA,CAAA,KAAMrE,YAAY,CAAC6D,MAAM,CAAE;QACtCpB,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEflJ,OAAA,CAACxB,MAAM;UACL0M,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEnL,OAAA,CAACN,cAAc;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB2B,IAAI,EAAC,OAAO;UACZ3K,OAAO,EAAE2B,aAAa,KAAK+I,MAAM,CAACpH,EAAG;UACrCyH,QAAQ,EAAE,CAACL,MAAM,CAAC3F,YAAY,IAAIpD,aAAa,KAAK+I,MAAM,CAACpH,EAAG;UAAAsF,QAAA,EAC/D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACbtJ,OAAA,CAACxB,MAAM;QACL2M,IAAI,eAAEnL,OAAA,CAACX,YAAY;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvB2B,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM5H,UAAU,CAACwH,MAAM,CAAE;QAAA9B,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtJ,OAAA,CAAChB,UAAU;QACTgK,KAAK,EAAC,oEAAa;QACnBwC,SAAS,EAAEA,CAAA,KAAM7H,YAAY,CAACqH,MAAM,CAACpH,EAAE,CAAE;QACzCgG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEflJ,OAAA,CAACxB,MAAM;UACLiN,MAAM;UACNN,IAAI,eAAEnL,OAAA,CAACV,cAAc;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB2B,IAAI,EAAC,OAAO;UAAA/B,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEtJ,OAAA;IAAAkJ,QAAA,gBACElJ,OAAA,CAAC1B,IAAI;MACH0K,KAAK,EAAC,0BAAM;MACZ0C,KAAK,eACH1L,OAAA,CAAClB,KAAK;QAAAoK,QAAA,gBACJlJ,OAAA,CAAChB,UAAU;UACTgK,KAAK,EAAC,kGAAkB;UACxBuC,WAAW,EAAC,sIAAwB;UACpCC,SAAS,EAAE7D,eAAgB;UAC3BiC,MAAM,EAAC,cAAI;UACXC,UAAU,EAAC,cAAI;UAAAX,QAAA,eAEflJ,OAAA,CAACxB,MAAM;YACL0M,IAAI,EAAC,SAAS;YACdC,IAAI,eAAEnL,OAAA,CAACN,cAAc;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBhJ,OAAO,EAAEA,OAAQ;YACjB+K,QAAQ,EAAEjL,QAAQ,CAACgG,MAAM,KAAK,CAAC,IAAI,CAAChG,QAAQ,CAACuL,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvG,YAAY,CAAE;YAAA6D,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACbtJ,OAAA,CAACxB,MAAM;UACL0M,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEnL,OAAA,CAACZ,YAAY;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB8B,OAAO,EAAE9H,SAAU;UAAA4F,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAEDlJ,OAAA,CAACzB,KAAK;QACJ8L,OAAO,EAAEA,OAAQ;QACjBwB,UAAU,EAAEzL,QAAS;QACrB0L,MAAM,EAAC,IAAI;QACXxL,OAAO,EAAEA,OAAQ;QACjByL,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGtF,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPtJ,OAAA,CAACvB,KAAK;MACJuK,KAAK,EAAEtI,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCyL,IAAI,EAAE3L,YAAa;MACnB4L,QAAQ,EAAEA,CAAA,KAAM3L,eAAe,CAAC,KAAK,CAAE;MACvCqJ,IAAI,EAAEA,CAAA,KAAMlJ,IAAI,CAACyL,MAAM,CAAC,CAAE;MAC1BzC,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAX,QAAA,eAEflJ,OAAA,CAACtB,IAAI;QACHkC,IAAI,EAAEA,IAAK;QACX0L,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExI,YAAa;QAAAmF,QAAA,gBAEvBlJ,OAAA,CAACtB,IAAI,CAAC8N,IAAI;UACRnF,IAAI,EAAC,MAAM;UACXrE,KAAK,EAAC,0BAAM;UACZyJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7N,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDlJ,OAAA,CAACrB,KAAK;YAACgO,WAAW,EAAC;UAAS;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZtJ,OAAA,CAACtB,IAAI,CAAC8N,IAAI;UACRnF,IAAI,EAAC,UAAU;UACfrE,KAAK,EAAC,0BAAM;UACZyJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7N,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDlJ,OAAA,CAACpB,MAAM;YAAC+N,WAAW,EAAC,4CAAS;YAAAzD,QAAA,EAC1BpG,eAAe,CAAC8J,GAAG,CAAClC,MAAM,iBACzB1K,OAAA,CAACC,MAAM;cAAoB8C,KAAK,EAAE2H,MAAM,CAAC3H,KAAM;cAAAmG,QAAA,EAC5CwB,MAAM,CAAC1H;YAAK,GADF0H,MAAM,CAAC3H,KAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRtJ,OAAA,CAACvB,KAAK;MACJuK,KAAK,EAAE,MAAMhI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqG,IAAI,EAAG;MAClC8E,IAAI,EAAErL,iBAAkB;MACxBsL,QAAQ,EAAEjH,qBAAsB;MAChC0H,MAAM,EAAE,cACN7M,OAAA,CAACxB,MAAM;QAAa4M,OAAO,EAAEjG,qBAAsB;QAAA+D,QAAA,EAAC;MAEpD,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTtJ,OAAA,CAACxB,MAAM;QAEL0M,IAAI,EAAC,SAAS;QACdE,OAAO,EAAEA,CAAA,KAAMpK,YAAY,IAAIsD,WAAW,CAACtD,YAAY,CAAE;QACzDqK,QAAQ,EAAEjK,YAAY,IAAIE,WAAW,KAAK,SAAU;QAAA4H,QAAA,EACrD;MAED,GANM,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CAAC,CACT;MACFwD,KAAK,EAAE,GAAI;MAAA5D,QAAA,eAEXlJ,OAAA;QAAKuJ,KAAK,EAAE;UAAEwD,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAA9D,QAAA,GACpD9H,YAAY,iBACXpB,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA,CAACd,IAAI;YAAC+L,IAAI,EAAC;UAAO;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAE0D,SAAS,EAAE;YAAG,CAAE;YAAA/D,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,EAEAhI,WAAW,KAAK,UAAU,IAAIJ,SAAS,iBACtClB,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA,CAACf,KAAK;YACJiO,GAAG,EAAEhM,SAAU;YACfiM,GAAG,EAAC,gCAAO;YACXL,KAAK,EAAE,GAAI;YACXM,MAAM,EAAE,GAAI;YACZ7D,KAAK,EAAE;cAAE8D,MAAM,EAAE;YAAoB;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACFtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAE0D,SAAS,EAAE,EAAE;cAAEvD,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,gBAC5ClJ,OAAA,CAACR,cAAc;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAE4D,QAAQ,EAAE;YAAO,CAAE;YAAApE,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAhI,WAAW,KAAK,SAAS,iBACxBtB,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA;YAAKuJ,KAAK,EAAE;cAAE+D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE,SAAS;cAAE6D,YAAY,EAAE;YAAG,CAAE;YAAArE,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEG,KAAK,EAAE,SAAS;cAAE4D,QAAQ,EAAE;YAAO,CAAE;YAAApE,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAhI,WAAW,KAAK,QAAQ,iBACvBtB,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA;YAAKuJ,KAAK,EAAE;cAAE+D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE,SAAS;cAAE6D,YAAY,EAAE;YAAG,CAAE;YAAArE,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEG,KAAK,EAAE,SAAS;cAAE4D,QAAQ,EAAE;YAAO,CAAE;YAAApE,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEAhI,WAAW,KAAK,SAAS,IAAI,CAACF,YAAY,iBACzCpB,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA,CAACR,cAAc;YAAC+J,KAAK,EAAE;cAAE+D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE,SAAS;cAAE6D,YAAY,EAAE;YAAG;UAAE;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFtJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtJ,OAAA,CAACvB,KAAK;MACJuK,KAAK,EAAE,UAAUtH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,EAAG;MACzC8E,IAAI,EAAE3K,oBAAqB;MAC3B4K,QAAQ,EAAElF,wBAAyB;MACnC4C,IAAI,EAAEA,CAAA,KAAM9H,YAAY,CAACqK,MAAM,CAAC,CAAE;MAClCzC,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACf2D,cAAc,EAAE5L,eAAgB;MAChCkL,KAAK,EAAE,GAAI;MAAA5D,QAAA,eAEXlJ,OAAA,CAACtB,IAAI;QACHkC,IAAI,EAAEoB,YAAa;QACnBsK,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAErG,oBAAqB;QAAAgD,QAAA,gBAE/BlJ,OAAA,CAACtB,IAAI,CAAC8N,IAAI;UACRnF,IAAI,EAAC,YAAY;UACjBrE,KAAK,EAAC,0BAAM;UACZyJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7N,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDlJ,OAAA,CAACrB,KAAK;YAACuM,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtJ,OAAA,CAACtB,IAAI,CAAC8N,IAAI;UACRnF,IAAI,EAAC,UAAU;UACfrE,KAAK,EAAC,0BAAM;UACZyJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7N,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDlJ,OAAA,CAACrB,KAAK;YAACuM,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZtJ,OAAA,CAACtB,IAAI,CAAC8N,IAAI;UACRnF,IAAI,EAAC,YAAY;UACjBrE,KAAK,EAAC,sCAAQ;UACdyJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7N,OAAO,EAAE;UAAc,CAAC,CAAE;UAAAqK,QAAA,eAEpDlJ,OAAA,CAACb,QAAQ,CAACsO,KAAK;YAAClE,KAAK,EAAE;cAAEuD,KAAK,EAAE;YAAO,CAAE;YAAA5D,QAAA,eACvClJ,OAAA;cAAKuJ,KAAK,EAAE;gBAAEmE,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAM,CAAE;cAAA1E,QAAA,gBACnElJ,OAAA,CAACb,QAAQ;gBAAC4D,KAAK,EAAC,eAAe;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpDtJ,OAAA,CAACb,QAAQ;gBAAC4D,KAAK,EAAC,gBAAgB;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACrDtJ,OAAA,CAACb,QAAQ;gBAAC4D,KAAK,EAAC,gBAAgB;gBAAAmG,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtDtJ,OAAA,CAACb,QAAQ;gBAAC4D,KAAK,EAAC,cAAc;gBAAAmG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEXxH,gBAAgB,iBACf9B,OAAA;UAAKuJ,KAAK,EAAE;YAAEsE,UAAU,EAAE,SAAS;YAAEb,OAAO,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEP,YAAY,EAAE;UAAO,CAAE;UAAArE,QAAA,eAChGlJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEE,MAAM,EAAE,CAAC;cAAE6D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACzDpH;UAAgB;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAEDtJ,OAAA;UAAKuJ,KAAK,EAAE;YAAEsE,UAAU,EAAE,SAAS;YAAEb,OAAO,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,SAAS,EAAE;UAAO,CAAE;UAAA/D,QAAA,gBAC7FlJ,OAAA;YAAGuJ,KAAK,EAAE;cAAEE,MAAM,EAAE,CAAC;cAAE6D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,eACvDlJ,OAAA;cAAAkJ,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJtJ,OAAA;YAAIuJ,KAAK,EAAE;cAAEE,MAAM,EAAE,WAAW;cAAED,WAAW,EAAE,MAAM;cAAE8D,QAAQ,EAAE,MAAM;cAAE5D,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,gBACvFlJ,OAAA;cAAAkJ,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtJ,OAAA;cAAAkJ,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAKRtJ,OAAA,CAACvB,KAAK;MACJuK,KAAK,EAAE,YAAY3G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgF,IAAI,EAAG;MACzC8E,IAAI,EAAEhK,sBAAuB;MAC7BiK,QAAQ,EAAEA,CAAA,KAAM;QACdhK,yBAAyB,CAAC,KAAK,CAAC;QAChCE,gBAAgB,CAAC,IAAI,CAAC;QACtBO,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAE;MACFiH,IAAI,EAAEtB,0BAA2B;MACjCoB,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACf2D,cAAc,EAAEnL,aAAa,GAAGE,qBAAqB,CAAC+I,GAAG,CAACjJ,aAAa,CAACuB,EAAE,CAAC,GAAG,KAAM;MACpFkJ,KAAK,EAAE,GAAI;MAAA5D,QAAA,gBAEXlJ,OAAA;QAAKuJ,KAAK,EAAE;UAAEgE,YAAY,EAAE;QAAG,CAAE;QAAArE,QAAA,gBAC/BlJ,OAAA;UAAAkJ,QAAA,EAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnBtJ,OAAA,CAACpB,MAAM;UACL2K,KAAK,EAAE;YAAEuD,KAAK,EAAE;UAAO,CAAE;UACzBH,WAAW,EAAC,4CAAS;UACrB5J,KAAK,EAAEH,mBAAoB;UAC3BmL,QAAQ,EAAElL,sBAAuB;UACjCmL,UAAU;UACVC,gBAAgB,EAAC,UAAU;UAAA/E,QAAA,EAE1BxG,UAAU,CAACkK,GAAG,CAACsB,GAAG,iBACjBlO,OAAA,CAACC,MAAM;YAAc8C,KAAK,EAAEmL,GAAG,CAACtK,EAAG;YAAAsF,QAAA,GAChCgF,GAAG,CAAC7G,IAAI,EAAC,KAAG,EAAC6G,GAAG,CAACC,YAAY;UAAA,GADnBD,GAAG,CAACtK,EAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtJ,OAAA;QAAKuJ,KAAK,EAAE;UAAEsE,UAAU,EAAE,SAAS;UAAEb,OAAO,EAAE,MAAM;UAAEc,YAAY,EAAE;QAAM,CAAE;QAAA5E,QAAA,gBAC1ElJ,OAAA;UAAGuJ,KAAK,EAAE;YAAEE,MAAM,EAAE,CAAC;YAAE6D,QAAQ,EAAE,MAAM;YAAE5D,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,eACvDlJ,OAAA;YAAAkJ,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJtJ,OAAA;UAAIuJ,KAAK,EAAE;YAAEE,MAAM,EAAE,WAAW;YAAED,WAAW,EAAE,MAAM;YAAE8D,QAAQ,EAAE,MAAM;YAAE5D,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACvFlJ,OAAA;YAAAkJ,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBtJ,OAAA;YAAAkJ,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCtJ,OAAA;YAAAkJ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnJ,EAAA,CAn3BID,aAAuB;EAAA,QAKZxB,IAAI,CAACmC,OAAO,EAcJnC,IAAI,CAACmC,OAAO;AAAA;AAAAuN,EAAA,GAnB/BlO,aAAuB;AAq3B7B,eAAeA,aAAa;AAAC,IAAAkO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}