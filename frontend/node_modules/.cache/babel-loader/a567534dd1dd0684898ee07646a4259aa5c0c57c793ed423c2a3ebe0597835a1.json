{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport AccountManage from './pages/AccountManage';\nimport FeishuAppManage from './pages/FeishuAppManage';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"accounts\",\n              element: /*#__PURE__*/_jsxDEV(AccountManage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"feishu-apps\",\n              element: /*#__PURE__*/_jsxDEV(FeishuAppManage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "Layout", "<PERSON><PERSON>", "Dashboard", "AccountManage", "FeishuAppManage", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "jsxDEV", "_jsxDEV", "App", "locale", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport AccountManage from './pages/AccountManage';\nimport FeishuAppManage from './pages/FeishuAppManage';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\n\nfunction App() {\n  return (\n    <ConfigProvider locale={zhCN}>\n      <AuthProvider>\n        <Router>\n          <Routes>\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <Layout />\n              </ProtectedRoute>\n            }>\n              <Route index element={<Dashboard />} />\n              <Route path=\"accounts\" element={<AccountManage />} />\n              <Route path=\"feishu-apps\" element={<FeishuAppManage />} />\n            </Route>\n          </Routes>\n        </Router>\n      </AuthProvider>\n    </ConfigProvider>\n  );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,cAAc;IAACY,MAAM,EAAEX,IAAK;IAAAY,QAAA,eAC3BH,OAAA,CAACH,YAAY;MAAAM,QAAA,eACXH,OAAA,CAACb,MAAM;QAAAgB,QAAA,eACLH,OAAA,CAACZ,MAAM;UAAAe,QAAA,gBACLH,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACP,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CT,OAAA,CAACX,KAAK;YAACe,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBL,OAAA,CAACF,cAAc;cAAAK,QAAA,eACbH,OAAA,CAACR,MAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACjB;YAAAN,QAAA,gBACCH,OAAA,CAACX,KAAK;cAACqB,KAAK;cAACL,OAAO,eAAEL,OAAA,CAACN,SAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCT,OAAA,CAACX,KAAK;cAACe,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEL,OAAA,CAACL,aAAa;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDT,OAAA,CAACX,KAAK;cAACe,IAAI,EAAC,aAAa;cAACC,OAAO,eAAEL,OAAA,CAACJ,eAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB;AAACE,EAAA,GArBQV,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}