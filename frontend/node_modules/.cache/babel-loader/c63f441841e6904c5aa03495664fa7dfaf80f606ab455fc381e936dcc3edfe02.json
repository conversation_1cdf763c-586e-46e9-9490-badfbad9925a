{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8000';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL\n});\n\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// 响应拦截器 - 处理错误\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// 飞书应用相关接口\n\nexport const feishuAppService = {\n  // 获取飞书应用列表\n  async getFeishuApps() {\n    const response = await api.get('/api/feishu-apps/');\n    return response.data;\n  },\n  // 创建飞书应用\n  async createFeishuApp(data) {\n    const response = await api.post('/api/feishu-apps/', data);\n    return response.data;\n  },\n  // 获取飞书应用详情\n  async getFeishuApp(id) {\n    const response = await api.get(`/api/feishu-apps/${id}`);\n    return response.data;\n  },\n  // 更新飞书应用\n  async updateFeishuApp(id, data) {\n    const response = await api.put(`/api/feishu-apps/${id}`, data);\n    return response.data;\n  },\n  // 删除飞书应用\n  async deleteFeishuApp(id) {\n    const response = await api.delete(`/api/feishu-apps/${id}`);\n    return response.data;\n  },\n  // 为飞书应用创建多维表格\n  async createBitableForApp(id, data) {\n    const response = await api.post(`/api/feishu-apps/${id}/create-bitable`, data);\n    return response.data;\n  },\n  // 获取已创建多维表格的飞书应用列表\n  async getFeishuAppsWithBitable() {\n    const response = await api.get('/api/feishu-apps/with-bitable');\n    return response.data;\n  }\n};\nexport default feishuAppService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "feishuAppService", "getFeishuApps", "get", "data", "createFeishuApp", "post", "getFeishuApp", "id", "updateFeishuApp", "put", "deleteFeishuApp", "delete", "createBitableForApp", "getFeishuAppsWithBitable"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n});\n\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// 响应拦截器 - 处理错误\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 飞书应用相关接口\nexport interface FeishuApp {\n  id: number;\n  name: string;\n  app_id: string;\n  user_id: number;\n  is_deleted: boolean;\n  created_at: string;\n  updated_at: string;\n  bitable_name?: string;\n  app_token?: string;\n  folder_token?: string;\n  url?: string;\n  bitable_created_at?: string;\n  has_bitable: boolean;\n}\n\nexport interface FeishuAppCreate {\n  name: string;\n  app_id: string;\n  app_secret: string;\n}\n\nexport interface FeishuAppUpdate {\n  name?: string;\n  app_id?: string;\n  app_secret?: string;\n}\n\nexport interface BitableCreateRequest {\n  bitable_name?: string;\n  folder_token?: string;\n}\n\nexport interface BitableCreateResponse {\n  success: boolean;\n  message: string;\n  app_token: string;\n  folder_token?: string;\n  url?: string;\n  bitable_name: string;\n}\n\nexport const feishuAppService = {\n  // 获取飞书应用列表\n  async getFeishuApps(): Promise<FeishuApp[]> {\n    const response = await api.get('/api/feishu-apps/');\n    return response.data;\n  },\n\n  // 创建飞书应用\n  async createFeishuApp(data: FeishuAppCreate): Promise<FeishuApp> {\n    const response = await api.post('/api/feishu-apps/', data);\n    return response.data;\n  },\n\n  // 获取飞书应用详情\n  async getFeishuApp(id: number): Promise<FeishuApp> {\n    const response = await api.get(`/api/feishu-apps/${id}`);\n    return response.data;\n  },\n\n  // 更新飞书应用\n  async updateFeishuApp(id: number, data: FeishuAppUpdate): Promise<FeishuApp> {\n    const response = await api.put(`/api/feishu-apps/${id}`, data);\n    return response.data;\n  },\n\n  // 删除飞书应用\n  async deleteFeishuApp(id: number): Promise<{ message: string }> {\n    const response = await api.delete(`/api/feishu-apps/${id}`);\n    return response.data;\n  },\n\n  // 为飞书应用创建多维表格\n  async createBitableForApp(id: number, data: BitableCreateRequest): Promise<BitableCreateResponse> {\n    const response = await api.post(`/api/feishu-apps/${id}/create-bitable`, data);\n    return response.data;\n  },\n\n  // 获取已创建多维表格的飞书应用列表\n  async getFeishuAppsWithBitable(): Promise<FeishuApp[]> {\n    const response = await api.get('/api/feishu-apps/with-bitable');\n    return response.data;\n  },\n};\n\nexport default feishuAppService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,uBAAuB;;AAE5C;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH;AACX,CAAC,CAAC;;AAEF;AACAC,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAN,GAAG,CAACG,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC1BO,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AA2CA,OAAO,MAAMS,gBAAgB,GAAG;EAC9B;EACA,MAAMC,aAAaA,CAAA,EAAyB;IAC1C,MAAMX,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAOZ,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMC,eAAeA,CAACD,IAAqB,EAAsB;IAC/D,MAAMb,QAAQ,GAAG,MAAMZ,GAAG,CAAC2B,IAAI,CAAC,mBAAmB,EAAEF,IAAI,CAAC;IAC1D,OAAOb,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMG,YAAYA,CAACC,EAAU,EAAsB;IACjD,MAAMjB,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,GAAG,CAAC,oBAAoBK,EAAE,EAAE,CAAC;IACxD,OAAOjB,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMK,eAAeA,CAACD,EAAU,EAAEJ,IAAqB,EAAsB;IAC3E,MAAMb,QAAQ,GAAG,MAAMZ,GAAG,CAAC+B,GAAG,CAAC,oBAAoBF,EAAE,EAAE,EAAEJ,IAAI,CAAC;IAC9D,OAAOb,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMO,eAAeA,CAACH,EAAU,EAAgC;IAC9D,MAAMjB,QAAQ,GAAG,MAAMZ,GAAG,CAACiC,MAAM,CAAC,oBAAoBJ,EAAE,EAAE,CAAC;IAC3D,OAAOjB,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMS,mBAAmBA,CAACL,EAAU,EAAEJ,IAA0B,EAAkC;IAChG,MAAMb,QAAQ,GAAG,MAAMZ,GAAG,CAAC2B,IAAI,CAAC,oBAAoBE,EAAE,iBAAiB,EAAEJ,IAAI,CAAC;IAC9E,OAAOb,QAAQ,CAACa,IAAI;EACtB,CAAC;EAED;EACA,MAAMU,wBAAwBA,CAAA,EAAyB;IACrD,MAAMvB,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,GAAG,CAAC,+BAA+B,CAAC;IAC/D,OAAOZ,QAAQ,CAACa,IAAI;EACtB;AACF,CAAC;AAED,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}