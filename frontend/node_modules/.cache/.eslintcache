[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx": "10", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts": "11"}, {"size": 272, "mtime": *************, "results": "12", "hashOfConfig": "13"}, {"size": 1163, "mtime": *************, "results": "14", "hashOfConfig": "15"}, {"size": 2178, "mtime": *************, "results": "16", "hashOfConfig": "15"}, {"size": 646, "mtime": *************, "results": "17", "hashOfConfig": "13"}, {"size": 1976, "mtime": *************, "results": "18", "hashOfConfig": "13"}, {"size": 5101, "mtime": *************, "results": "19", "hashOfConfig": "15"}, {"size": 30792, "mtime": *************, "results": "20", "hashOfConfig": "15"}, {"size": 2050, "mtime": *************, "results": "21", "hashOfConfig": "13"}, {"size": 721, "mtime": 1752751191617, "results": "22", "hashOfConfig": "13"}, {"size": 10389, "mtime": 1753033841869, "results": "23", "hashOfConfig": "15"}, {"size": 2912, "mtime": 1753033799016, "results": "24", "hashOfConfig": "15"}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sxxfvo", {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ix7n9v", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", ["58", "59", "60"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", ["61"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts", [], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 242, "column": 49, "nodeType": "64", "messageId": "65", "endLine": 242, "endColumn": 60}, {"ruleId": "62", "severity": 1, "message": "66", "line": 300, "column": 9, "nodeType": "64", "messageId": "65", "endLine": 300, "endColumn": 26}, {"ruleId": "62", "severity": 1, "message": "67", "line": 470, "column": 31, "nodeType": "64", "messageId": "65", "endLine": 470, "endColumn": 43}, {"ruleId": "62", "severity": 1, "message": "68", "line": 2, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 2, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'total_files' is assigned a value but never used.", "Identifier", "unusedVar", "'handleForceLogout' is assigned a value but never used.", "'data_summary' is assigned a value but never used.", "'message' is defined but never used."]