#!/usr/bin/env python3
"""
飞书多维表格集成完整使用示例
演示从创建表格到同步数据的完整流程
"""
import os
import sys
import asyncio
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

async def complete_workflow_example():
    """完整工作流程示例"""
    print("=" * 80)
    print("飞书多维表格集成完整工作流程示例")
    print("=" * 80)
    
    # 检查环境变量
    app_id = os.getenv("FEISHU_APP_ID")
    app_secret = os.getenv("FEISHU_APP_SECRET")
    
    if not app_id or not app_secret:
        print("❌ 请先设置环境变量 FEISHU_APP_ID 和 FEISHU_APP_SECRET")
        print("   在 .env 文件中添加:")
        print("   FEISHU_APP_ID=your-app-id")
        print("   FEISHU_APP_SECRET=your-app-secret")
        return False
    
    try:
        from app.services.feishu_service import FeishuService
        from app.services.data_converter import WeChatDataConverter
        
        # 步骤1: 初始化飞书服务
        print("\n📋 步骤1: 初始化飞书服务")
        print("-" * 40)
        feishu_service = FeishuService()
        print("✅ 飞书服务初始化成功")
        
        # 步骤2: 创建多维表格
        print("\n📋 步骤2: 创建多维表格")
        print("-" * 40)
        import time
        bitable_name = f"微信公众号数据分析_{int(time.time())}"
        bitable_info = feishu_service.create_bitable(bitable_name)

        if not bitable_info:
            print("❌ 创建多维表格失败")
            return False

        app_token = bitable_info.get("app_token")
        print(f"✅ 多维表格创建成功: {app_token}")
        print(f"   URL: {bitable_info.get('url')}")
        print(f"   Folder Token: {bitable_info.get('folder_token')}")
        
        print(f"✅ 多维表格创建成功")
        print(f"   表格名称: {bitable_name}")
        print(f"   App Token: {app_token}")
        
        # 步骤3: 创建数据表和字段
        print("\n📋 步骤3: 创建数据表和字段")
        print("-" * 40)
        
        # 创建用户概况数据表
        print("🔄 创建用户概况数据表...")
        user_table_id = feishu_service.create_table_with_fields(app_token, "用户概况数据", "user_summary")
        if user_table_id:
            print(f"✅ 用户概况数据表创建成功: {user_table_id}")
        else:
            print("❌ 用户概况数据表创建失败")
            return False
        
        # 创建图文分析数据表
        print("🔄 创建图文分析数据表...")
        article_table_id = feishu_service.create_table_with_fields(app_token, "图文分析数据", "article_summary")
        if article_table_id:
            print(f"✅ 图文分析数据表创建成功: {article_table_id}")
        else:
            print("❌ 图文分析数据表创建失败")
            return False
        
        # 步骤4: 验证字段创建
        print("\n📋 步骤4: 验证字段创建")
        print("-" * 40)
        
        user_fields = feishu_service.get_table_fields(app_token, user_table_id)
        if user_fields:
            print(f"✅ 用户概况表字段验证成功 ({len(user_fields)} 个字段):")
            for field in user_fields:
                print(f"   - {field.get('field_name', 'Unknown')}")
        
        article_fields = feishu_service.get_table_fields(app_token, article_table_id)
        if article_fields:
            print(f"✅ 图文分析表字段验证成功 ({len(article_fields)} 个字段):")
            for field in article_fields:
                print(f"   - {field.get('field_name', 'Unknown')}")
        
        # 步骤5: 准备测试数据
        print("\n📋 步骤5: 准备测试数据")
        print("-" * 40)
        
        # 创建模拟的微信数据
        import pandas as pd
        from io import BytesIO
        
        # 用户概况测试数据
        user_test_data = {
            "日期": ["2024-01-01", "2024-01-02", "2024-01-03"],
            "新增用户": [100, 120, 80],
            "取消关注用户": [10, 15, 5],
            "净增长": [90, 105, 75],
            "累计用户": [1000, 1105, 1180]
        }
        
        # 图文分析测试数据
        article_test_data = {
            "标题": ["新年祝福", "产品介绍", "活动通知"],
            "阅读数": [1500, 800, 1200],
            "点赞数": [75, 40, 60],
            "分享数": [30, 15, 25],
            "发布时间": ["2024-01-01 10:00:00", "2024-01-02 14:00:00", "2024-01-03 16:00:00"]
        }
        
        # 创建Excel文件
        excel_buffer = BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            pd.DataFrame(user_test_data).to_excel(writer, sheet_name='用户概况', index=False)
            pd.DataFrame(article_test_data).to_excel(writer, sheet_name='图文分析', index=False)
        
        excel_data = excel_buffer.getvalue()
        print(f"✅ 测试Excel数据创建成功 ({len(excel_data)} bytes)")
        
        # 步骤6: 解析数据
        print("\n📋 步骤6: 解析Excel数据")
        print("-" * 40)
        
        parsed_data = WeChatDataConverter.parse_excel_data(excel_data)
        if parsed_data:
            print("✅ Excel数据解析成功")
            print(f"   用户概况数据: {len(parsed_data['user_summary'])} 条")
            print(f"   图文分析数据: {len(parsed_data['article_summary'])} 条")
            print(f"   发现的sheet: {parsed_data['raw_sheets']}")
        else:
            print("❌ Excel数据解析失败")
            return False
        
        # 步骤7: 转换为飞书格式
        print("\n📋 步骤7: 转换为飞书格式")
        print("-" * 40)
        
        user_records = WeChatDataConverter.prepare_feishu_records(
            "user_summary", 
            parsed_data["user_summary"]
        )
        
        article_records = WeChatDataConverter.prepare_feishu_records(
            "article_summary",
            parsed_data["article_summary"]
        )
        
        print(f"✅ 数据格式转换成功")
        print(f"   用户记录: {len(user_records)} 条")
        print(f"   图文记录: {len(article_records)} 条")
        
        # 步骤8: 写入数据到飞书
        print("\n📋 步骤8: 写入数据到飞书")
        print("-" * 40)
        
        # 写入用户概况数据
        if user_records:
            user_record_ids = feishu_service.batch_create_records(app_token, user_table_id, user_records)
            if user_record_ids:
                print(f"✅ 用户概况数据写入成功: {len(user_record_ids)} 条记录")
            else:
                print("❌ 用户概况数据写入失败")
        
        # 写入图文分析数据
        if article_records:
            article_record_ids = feishu_service.batch_create_records(app_token, article_table_id, article_records)
            if article_record_ids:
                print(f"✅ 图文分析数据写入成功: {len(article_record_ids)} 条记录")
            else:
                print("❌ 图文分析数据写入失败")
        
        # 步骤9: 验证数据写入
        print("\n📋 步骤9: 验证数据写入")
        print("-" * 40)
        
        # 获取用户概况表记录
        user_records_check = feishu_service.list_records(app_token, user_table_id)
        if user_records_check:
            print(f"✅ 用户概况表验证成功: {len(user_records_check)} 条记录")
        
        # 获取图文分析表记录
        article_records_check = feishu_service.list_records(app_token, article_table_id)
        if article_records_check:
            print(f"✅ 图文分析表验证成功: {len(article_records_check)} 条记录")
        
        # 完成
        print("\n" + "=" * 80)
        print("🎉 完整工作流程执行成功！")
        print("=" * 80)
        print(f"多维表格信息:")
        print(f"  表格名称: {bitable_name}")
        print(f"  App Token: {app_token}")
        print(f"  用户概况表ID: {user_table_id}")
        print(f"  图文分析表ID: {article_table_id}")
        print(f"\n可以在飞书中查看创建的多维表格和数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流程执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("飞书多维表格集成完整使用示例")
    print("此示例演示从创建表格到同步数据的完整流程")
    print("\n注意事项:")
    print("1. 需要有效的飞书应用凭证")
    print("2. 需要网络连接到飞书服务")
    print("3. 会在飞书中创建真实的多维表格")
    
    # 询问用户是否继续
    try:
        confirm = input("\n是否继续执行示例？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("已取消执行")
            return 0
    except KeyboardInterrupt:
        print("\n已取消执行")
        return 0
    
    # 执行示例
    success = asyncio.run(complete_workflow_example())
    
    if success:
        print("\n🎉 示例执行成功！")
        return 0
    else:
        print("\n❌ 示例执行失败！")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
