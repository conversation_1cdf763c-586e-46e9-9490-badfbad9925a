#!/usr/bin/env python3
"""
API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_auth_system():
    """测试认证系统"""
    print("=== 测试认证系统 ===")
    
    # 测试用户登录
    print("\n1. 测试用户登录...")
    login_response = requests.post(f'{BASE_URL}/api/auth/login', 
        json={
            'username': 'testuser',
            'password': 'testpassword123'
        })
    
    print(f"登录响应状态: {login_response.status_code}")
    if login_response.status_code == 200:
        login_data = login_response.json()
        print("✅ 登录成功!")
        print(f"用户信息: {login_data['user']}")
        print(f"Token类型: {login_data['token_type']}")
        return login_data['access_token']
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return None

def test_user_info(token):
    """测试获取用户信息"""
    print("\n2. 测试获取用户信息...")
    me_response = requests.get(f'{BASE_URL}/api/auth/me',
        headers={'Authorization': f'Bearer {token}'})
    
    print(f"用户信息响应状态: {me_response.status_code}")
    if me_response.status_code == 200:
        user_data = me_response.json()
        print("✅ 获取用户信息成功!")
        print(f"用户信息: {user_data}")
    else:
        print(f"❌ 获取用户信息失败: {me_response.text}")

def test_account_management(token):
    """测试账号管理"""
    print("\n=== 测试账号管理 ===")
    
    # 测试获取账号列表
    print("\n1. 测试获取账号列表...")
    accounts_response = requests.get(f'{BASE_URL}/api/accounts/',
        headers={'Authorization': f'Bearer {token}'})
    
    print(f"账号列表响应状态: {accounts_response.status_code}")
    if accounts_response.status_code == 200:
        accounts_data = accounts_response.json()
        print("✅ 获取账号列表成功!")
        print(f"现有账号数量: {len(accounts_data)}")
        for account in accounts_data:
            print(f"  - {account['name']} ({account['platform']}) - 登录状态: {account['login_status']}")
    else:
        print(f"❌ 获取账号列表失败: {accounts_response.text}")
    
    # 测试创建新账号
    print("\n2. 测试创建新账号...")
    import time
    account_name = f"测试小红书账号_{int(time.time())}"
    
    create_response = requests.post(f'{BASE_URL}/api/accounts/',
        json={
            'name': account_name,
            'platform': 'xiaohongshu'
        },
        headers={'Authorization': f'Bearer {token}'})
    
    print(f"创建账号响应状态: {create_response.status_code}")
    if create_response.status_code == 200:
        account_data = create_response.json()
        print("✅ 创建账号成功!")
        print(f"新账号信息: {account_data}")
        return account_data['id']
    else:
        print(f"❌ 创建账号失败: {create_response.text}")
        return None

def test_account_operations(token, account_id):
    """测试账号操作"""
    if not account_id:
        return
        
    print(f"\n3. 测试账号操作 (ID: {account_id})...")
    
    # 测试获取单个账号信息
    get_response = requests.get(f'{BASE_URL}/api/accounts/{account_id}',
        headers={'Authorization': f'Bearer {token}'})
    
    print(f"获取账号信息响应状态: {get_response.status_code}")
    if get_response.status_code == 200:
        account_data = get_response.json()
        print("✅ 获取账号信息成功!")
        print(f"账号信息: {account_data}")
    else:
        print(f"❌ 获取账号信息失败: {get_response.text}")
    
    # 测试更新账号
    update_response = requests.put(f'{BASE_URL}/api/accounts/{account_id}',
        json={'name': '更新后的账号名称'},
        headers={'Authorization': f'Bearer {token}'})
    
    print(f"更新账号响应状态: {update_response.status_code}")
    if update_response.status_code == 200:
        updated_data = update_response.json()
        print("✅ 更新账号成功!")
        print(f"更新后账号信息: {updated_data}")
    else:
        print(f"❌ 更新账号失败: {update_response.text}")

def main():
    """主测试函数"""
    try:
        print("开始API测试...")
        
        # 测试认证系统
        token = test_auth_system()
        if not token:
            print("❌ 认证失败，终止测试")
            return
        
        # 测试获取用户信息
        test_user_info(token)
        
        # 测试账号管理
        account_id = test_account_management(token)
        
        # 测试账号操作
        test_account_operations(token, account_id)
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()
