#!/usr/bin/env python3
"""
完整系统测试脚本
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_complete_system():
    """完整系统测试"""
    print("🚀 开始完整系统测试...")
    print("=" * 60)
    
    # 1. 测试用户认证系统
    print("\n📝 1. 测试用户认证系统")
    print("-" * 30)
    
    # 登录
    login_response = requests.post(f'{BASE_URL}/api/auth/login', 
        json={'username': 'testuser', 'password': 'testpassword123'})
    
    if login_response.status_code != 200:
        print("❌ 用户登录失败")
        return False
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ 用户登录成功")
    
    # 获取用户信息
    me_response = requests.get(f'{BASE_URL}/api/auth/me', headers=headers)
    if me_response.status_code == 200:
        user_info = me_response.json()
        print(f"✅ 获取用户信息成功: {user_info['username']}")
    else:
        print("❌ 获取用户信息失败")
        return False
    
    # 2. 测试账号管理系统
    print("\n🔧 2. 测试账号管理系统")
    print("-" * 30)
    
    # 获取账号列表
    accounts_response = requests.get(f'{BASE_URL}/api/accounts/', headers=headers)
    if accounts_response.status_code == 200:
        accounts = accounts_response.json()
        print(f"✅ 获取账号列表成功: {len(accounts)} 个账号")
        
        # 如果没有账号，创建一个测试账号
        if not accounts:
            create_response = requests.post(f'{BASE_URL}/api/accounts/',
                json={'name': '系统测试账号', 'platform': 'wechat_mp'},
                headers=headers)
            if create_response.status_code == 200:
                accounts = [create_response.json()]
                print("✅ 创建测试账号成功")
            else:
                print("❌ 创建测试账号失败")
                return False
    else:
        print("❌ 获取账号列表失败")
        return False
    
    # 3. 测试微信相关API
    print("\n📱 3. 测试微信相关API")
    print("-" * 30)
    
    if accounts:
        account_id = accounts[0]['id']
        
        # 测试获取登录二维码
        qr_response = requests.post(f'{BASE_URL}/api/wechat/login/qrcode/{account_id}', headers=headers)
        if qr_response.status_code == 200:
            print("✅ 获取登录二维码API正常")
        else:
            print("❌ 获取登录二维码API失败")
        
        # 测试检查登录状态
        status_response = requests.get(f'{BASE_URL}/api/wechat/login/status/{account_id}', headers=headers)
        if status_response.status_code == 200:
            print("✅ 检查登录状态API正常")
        else:
            print("❌ 检查登录状态API失败")
        
        # 测试获取已采集数据
        data_response = requests.get(f'{BASE_URL}/api/wechat/data/{account_id}', headers=headers)
        if data_response.status_code == 200:
            data_info = data_response.json()
            print(f"✅ 获取数据API正常: {len(data_info['data_records'])} 条记录")
        else:
            print("❌ 获取数据API失败")
    
    # 4. 测试数据分析API
    print("\n📊 4. 测试数据分析API")
    print("-" * 30)
    
    # Dashboard数据
    dashboard_response = requests.get(f'{BASE_URL}/api/analytics/dashboard', headers=headers)
    if dashboard_response.status_code == 200:
        dashboard_data = dashboard_response.json()
        print(f"✅ Dashboard API正常")
        print(f"   - 总账号数: {dashboard_data['total_accounts']}")
        print(f"   - 已登录账号: {dashboard_data['logged_in_accounts']}")
        print(f"   - 数据记录数: {dashboard_data['total_data_records']}")
    else:
        print("❌ Dashboard API失败")
    
    # 平台统计
    stats_response = requests.get(f'{BASE_URL}/api/analytics/platform-stats', headers=headers)
    if stats_response.status_code == 200:
        stats_data = stats_response.json()
        print(f"✅ 平台统计API正常: {len(stats_data)} 个平台")
    else:
        print("❌ 平台统计API失败")
    
    if accounts:
        account_id = accounts[0]['id']
        
        # 用户增长趋势
        growth_response = requests.get(f'{BASE_URL}/api/analytics/user-growth/{account_id}', headers=headers)
        if growth_response.status_code == 200:
            print("✅ 用户增长趋势API正常")
        else:
            print("❌ 用户增长趋势API失败")
        
        # 热门文章
        articles_response = requests.get(f'{BASE_URL}/api/analytics/top-articles/{account_id}', headers=headers)
        if articles_response.status_code == 200:
            print("✅ 热门文章API正常")
        else:
            print("❌ 热门文章API失败")
    
    # 5. 测试前端服务
    print("\n🌐 5. 测试前端服务")
    print("-" * 30)
    
    try:
        frontend_response = requests.get("http://localhost:3000/", timeout=5)
        if frontend_response.status_code == 200:
            print("✅ 前端服务正常运行")
        else:
            print("❌ 前端服务响应异常")
    except requests.exceptions.RequestException:
        print("⚠️  前端服务可能未启动")
    
    # 6. 测试API文档
    print("\n📚 6. 测试API文档")
    print("-" * 30)
    
    docs_response = requests.get(f'{BASE_URL}/docs')
    if docs_response.status_code == 200:
        print("✅ API文档可访问")
    else:
        print("❌ API文档不可访问")
    
    openapi_response = requests.get(f'{BASE_URL}/openapi.json')
    if openapi_response.status_code == 200:
        print("✅ OpenAPI规范可访问")
    else:
        print("❌ OpenAPI规范不可访问")
    
    print("\n" + "=" * 60)
    print("🎉 完整系统测试完成！")
    print("\n📋 系统功能总结:")
    print("✅ 用户认证系统 - 注册、登录、JWT认证")
    print("✅ 账号管理系统 - CRUD操作、多平台支持")
    print("✅ 微信登录接口 - 二维码获取、状态检测")
    print("✅ 数据采集接口 - 用户数据、图文数据")
    print("✅ 数据分析接口 - Dashboard、统计、趋势")
    print("✅ API文档系统 - Swagger UI、OpenAPI")
    print("\n🚀 系统已准备就绪，可以开始使用！")
    
    return True

def test_system_performance():
    """系统性能测试"""
    print("\n⚡ 系统性能测试")
    print("-" * 30)
    
    # 登录获取token
    login_response = requests.post(f'{BASE_URL}/api/auth/login', 
        json={'username': 'testuser', 'password': 'testpassword123'})
    
    if login_response.status_code != 200:
        print("❌ 无法获取认证token")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 测试API响应时间
    apis_to_test = [
        ('/api/auth/me', 'GET'),
        ('/api/accounts/', 'GET'),
        ('/api/analytics/dashboard', 'GET'),
        ('/api/analytics/platform-stats', 'GET'),
    ]
    
    for endpoint, method in apis_to_test:
        start_time = time.time()
        
        if method == 'GET':
            response = requests.get(f'{BASE_URL}{endpoint}', headers=headers)
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        status = "✅" if response.status_code == 200 else "❌"
        print(f"{status} {endpoint}: {response_time:.2f}ms")

if __name__ == "__main__":
    # 运行完整系统测试
    success = test_complete_system()
    
    if success:
        # 运行性能测试
        test_system_performance()
    
    print("\n" + "=" * 60)
    print("测试完成！")
