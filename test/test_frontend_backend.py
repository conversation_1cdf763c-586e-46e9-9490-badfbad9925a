#!/usr/bin/env python3
"""
前后端联调测试脚本
"""
import requests
import time
import json

def test_backend_api():
    """测试后端API是否正常"""
    print("=== 测试后端API ===")
    
    try:
        # 测试后端健康检查
        response = requests.get("http://localhost:8000/")
        print(f"后端根路径: {response.status_code} - {response.json()}")
        
        # 测试API文档
        response = requests.get("http://localhost:8000/docs")
        print(f"API文档: {response.status_code}")
        
        # 测试登录API
        login_response = requests.post("http://localhost:8000/api/auth/login", 
            json={
                "username": "testuser",
                "password": "testpassword123"
            })
        print(f"登录API: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            
            # 测试获取账号列表
            accounts_response = requests.get("http://localhost:8000/api/accounts/",
                headers={"Authorization": f"Bearer {token}"})
            print(f"账号列表API: {accounts_response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"后端API测试失败: {e}")
        return False

def test_frontend():
    """测试前端是否正常"""
    print("\n=== 测试前端应用 ===")
    
    try:
        # 测试前端首页
        response = requests.get("http://localhost:3000/")
        print(f"前端首页: {response.status_code}")
        
        # 测试前端静态资源
        response = requests.get("http://localhost:3000/static/js/bundle.js")
        print(f"前端JS资源: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"前端测试失败: {e}")
        return False

def test_cors():
    """测试CORS配置"""
    print("\n=== 测试CORS配置 ===")
    
    try:
        # 模拟前端跨域请求
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        
        # 预检请求
        response = requests.options("http://localhost:8000/api/auth/login", headers=headers)
        print(f"CORS预检请求: {response.status_code}")
        
        # 检查CORS头
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        print(f"CORS响应头: {cors_headers}")
        
        return True
        
    except Exception as e:
        print(f"CORS测试失败: {e}")
        return False

def test_proxy():
    """测试前端代理配置"""
    print("\n=== 测试前端代理 ===")
    
    try:
        # 通过前端代理访问后端API
        response = requests.get("http://localhost:3000/api/")
        print(f"前端代理到后端: {response.status_code}")
        
        if response.status_code == 200:
            print(f"代理响应: {response.json()}")
        
        return True
        
    except Exception as e:
        print(f"代理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始前后端联调测试...")
    print("=" * 50)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    results = []
    
    # 测试后端API
    results.append(("后端API", test_backend_api()))
    
    # 测试前端
    results.append(("前端应用", test_frontend()))
    
    # 测试CORS
    results.append(("CORS配置", test_cors()))
    
    # 测试代理
    results.append(("前端代理", test_proxy()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过！前后端联调成功！")
    else:
        print("\n⚠️  部分测试失败，请检查服务状态")
    
    return all_passed

if __name__ == "__main__":
    main()
