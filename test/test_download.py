#!/usr/bin/env python3
"""
测试微信公众号数据下载功能
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_service import WeChatMPService

async def test_download_functionality():
    """测试下载功能"""
    service = WeChatMPService()
    
    try:
        print("=" * 60)
        print("微信公众号数据下载功能测试")
        print("=" * 60)
        
        # 首先获取登录二维码
        print("\n📱 获取登录二维码...")
        qr_data = await service.get_login_qrcode()
        
        if not qr_data:
            print("❌ 获取二维码失败")
            return False
        
        print("✅ 二维码获取成功")
        
        # 保存二维码图片
        if qr_data.startswith('data:image/png;base64,'):
            import base64
            qr_base64 = qr_data.replace('data:image/png;base64,', '')
            with open('download_test_qrcode.png', 'wb') as f:
                f.write(base64.b64decode(qr_base64))
            print("📱 二维码已保存到: download_test_qrcode.png")
        
        print("\n" + "="*60)
        print("⚠️  测试提示:")
        print("由于下载功能需要登录状态，请按以下步骤测试:")
        print("1. 用微信扫描生成的二维码: download_test_qrcode.png")
        print("2. 扫码成功后，运行完整的下载测试")
        print("="*60)
        
        # 测试日期格式化功能
        print("\n🔧 测试日期格式化功能...")
        
        test_dates = [
            "2025-07-18",
            "2025-7-18", 
            "20250718",
            "2025/07/18",
            "2025/7/18"
        ]
        
        for date_str in test_dates:
            formatted = service._format_date_for_download(date_str)
            print(f"   {date_str} -> {formatted}")
        
        # 测试下载URL构建
        print("\n🔗 测试下载URL构建...")
        
        # 模拟token
        service.page = type('obj', (object,), {'url': 'https://mp.weixin.qq.com/?token=test123456'})()
        
        begin_date = "2025-06-17"
        end_date = "2025-07-17"
        
        token = service._extract_token_from_url()
        formatted_begin = service._format_date_for_download(begin_date)
        formatted_end = service._format_date_for_download(end_date)
        
        print(f"   Token: {token}")
        print(f"   开始日期: {begin_date} -> {formatted_begin}")
        print(f"   结束日期: {end_date} -> {formatted_end}")
        
        # 构建下载URL
        download_url = f"https://mp.weixin.qq.com/misc/datacubequery?action=query_download&busi=3&tmpl=19&args={{\"begin_date\":{formatted_begin},\"end_date\":{formatted_end}}}&token={token}&lang=zh_CN"
        print(f"   下载URL: {download_url}")
        
        print("\n✅ URL构建测试完成")
        
        print("\n" + "="*60)
        print("📋 下载功能测试总结:")
        print("✅ 日期格式化功能正常")
        print("✅ Token提取功能正常") 
        print("✅ URL构建功能正常")
        print("⚠️  完整下载测试需要实际登录状态")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await service.close()

async def test_download_with_login():
    """带登录的完整下载测试（需要手动扫码）"""
    service = WeChatMPService()
    
    try:
        print("=" * 60)
        print("完整下载功能测试（需要登录）")
        print("=" * 60)
        
        # 获取登录二维码
        print("\n📱 获取登录二维码...")
        qr_data = await service.get_login_qrcode()
        
        if not qr_data:
            print("❌ 获取二维码失败")
            return False
        
        # 保存二维码
        if qr_data.startswith('data:image/png;base64,'):
            import base64
            qr_base64 = qr_data.replace('data:image/png;base64,', '')
            with open('login_for_download.png', 'wb') as f:
                f.write(base64.b64decode(qr_base64))
            print("📱 二维码已保存到: login_for_download.png")
        
        print("\n⏳ 等待扫码登录（30秒）...")
        print("请用微信扫描二维码完成登录")
        
        # 等待登录
        login_success = await service.wait_for_login_complete(max_wait_time=30)
        
        if not login_success:
            print("❌ 登录失败或超时")
            return False
        
        print("✅ 登录成功！")
        
        # 测试下载功能
        print("\n📥 测试数据下载...")
        
        # 设置日期范围（前一天结束，向前30天）
        today = datetime.now()
        end_date = today - timedelta(days=1)  # 前一天
        start_date = end_date - timedelta(days=29)  # 从结束日期向前29天，总共30天
        
        begin_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        print(f"下载日期范围: {begin_date_str} 到 {end_date_str} (共30天)")
        
        # 下载数据
        excel_data = await service.download_data_excel(
            begin_date=begin_date_str,
            end_date=end_date_str
        )
        
        if excel_data:
            # 保存下载的文件
            filename = f"downloaded_data_{begin_date_str}_to_{end_date_str}.xlsx"
            with open(filename, 'wb') as f:
                f.write(excel_data)
            
            print(f"✅ 数据下载成功！")
            print(f"文件大小: {len(excel_data)} bytes")
            print(f"文件已保存为: {filename}")
            return True
        else:
            print("❌ 数据下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await service.close()

async def main():
    """主函数"""
    print("微信公众号数据下载功能测试")
    print("请选择测试模式:")
    print("1. 基础功能测试（不需要登录）")
    print("2. 完整下载测试（需要扫码登录）")
    
    # 默认执行基础功能测试
    choice = "1"
    
    try:
        if choice == "1":
            success = await test_download_functionality()
        else:
            success = await test_download_with_login()
        
        if success:
            print("\n🎉 测试完成！")
            return 0
        else:
            print("\n❌ 测试失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
