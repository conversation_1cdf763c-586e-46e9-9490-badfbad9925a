#!/usr/bin/env python3
"""
微信公众号登录演示脚本 - 完整登录流程
"""
import asyncio
import sys
import os
import base64

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_service import WeChatMPService

async def demonstrate_login_flow():
    """演示完整的微信公众号登录流程"""
    service = WeChatMPService()
    
    try:
        print("=" * 60)
        print("微信公众号登录演示")
        print("=" * 60)
        
        # 步骤1: 获取登录二维码
        print("\n🔄 步骤1: 获取登录二维码...")
        qr_data = await service.get_login_qrcode()
        
        if not qr_data:
            print("❌ 获取二维码失败")
            return False
        
        print("✅ 二维码获取成功")
        
        # 保存二维码图片
        if qr_data.startswith('data:image/png;base64,'):
            qr_base64 = qr_data.replace('data:image/png;base64,', '')
            with open('login_qrcode.png', 'wb') as f:
                f.write(base64.b64decode(qr_base64))
            print(f"📱 二维码已保存到: login_qrcode.png")
            print("   请使用微信扫描此二维码")
        
        # 步骤2: 等待用户扫码登录
        print("\n⏳ 步骤2: 等待扫码登录...")
        print("请在30秒内使用微信扫描二维码完成登录")
        print("如果不想测试登录，请按 Ctrl+C 跳过")
        
        try:
            # 等待登录完成（30秒超时）
            login_success = await service.wait_for_login_complete(max_wait_time=30)
            
            if login_success:
                print("🎉 登录成功！")
                
                # 步骤3: 获取登录后的信息
                print("\n📊 步骤3: 获取登录信息...")
                
                # 获取cookies
                cookies = await service.get_cookies()
                if cookies:
                    print("✅ Cookies获取成功")
                    print(f"Cookies长度: {len(cookies)} 字符")
                else:
                    print("❌ Cookies获取失败")
                
                # 再次检查登录状态
                final_status = await service.check_login_status(wait_for_redirect=False)
                print(f"最终登录状态: {'✅ 已登录' if final_status else '❌ 未登录'}")
                
                return True
            else:
                print("❌ 登录失败或超时")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️  用户取消了登录测试")
            return False
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await service.close()

async def quick_qrcode_test():
    """快速二维码测试（不等待登录）"""
    service = WeChatMPService()
    
    try:
        print("=" * 60)
        print("快速二维码获取测试")
        print("=" * 60)
        
        # 测试多次获取二维码的稳定性
        test_count = 3
        success_count = 0
        
        for i in range(test_count):
            print(f"\n🔄 第 {i+1} 次测试...")
            
            # 如果不是第一次，先关闭之前的连接
            if i > 0:
                await service.close()
                await asyncio.sleep(1)
            
            qr_data = await service.get_login_qrcode()
            
            if qr_data and len(qr_data) > 100:
                success_count += 1
                print(f"✅ 第 {i+1} 次成功 - 数据长度: {len(qr_data)}")
                
                # 保存二维码
                if qr_data.startswith('data:image/png;base64,'):
                    qr_base64 = qr_data.replace('data:image/png;base64,', '')
                    with open(f'qrcode_test_{i+1}.png', 'wb') as f:
                        f.write(base64.b64decode(qr_base64))
                    print(f"   二维码已保存: qrcode_test_{i+1}.png")
            else:
                print(f"❌ 第 {i+1} 次失败")
        
        success_rate = (success_count / test_count) * 100
        print(f"\n📈 测试结果: {success_count}/{test_count} 成功 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("✅ 稳定性测试通过")
        else:
            print("❌ 稳定性需要改进")
            
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    
    finally:
        await service.close()

async def main():
    """主函数"""
    print("微信公众号登录工具")
    print("请选择测试模式:")
    print("1. 完整登录流程演示（包含扫码登录）")
    print("2. 快速二维码获取测试（不等待登录）")
    
    try:
        # 由于这是自动化脚本，我们默认执行快速测试
        # 在实际使用时，可以添加用户输入选择
        choice = "2"  # 默认选择快速测试
        
        if choice == "1":
            success = await demonstrate_login_flow()
        else:
            success = await quick_qrcode_test()
        
        if success:
            print("\n🎉 测试完成！")
            return 0
        else:
            print("\n❌ 测试失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  程序被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 程序发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
