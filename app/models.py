from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    accounts = relationship("PlatformAccount", back_populates="owner")
    feishu_apps = relationship("FeishuApp", back_populates="owner")

class FeishuApp(Base):
    __tablename__ = "feishu_apps"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))  # App名称
    app_id = Column(String(255))  # 飞书应用ID
    app_secret = Column(String(255))  # 飞书应用密钥
    user_id = Column(Integer, ForeignKey("users.id"))
    is_deleted = Column(Boolean, default=False)  # 删除标志
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 多维表格相关字段（一对一关系）
    bitable_name = Column(String(255))  # 多维表格名称
    app_token = Column(String(255))  # 多维表格token
    folder_token = Column(String(255))  # 文件夹token
    url = Column(String(500))  # 访问URL
    bitable_created_at = Column(DateTime)  # 多维表格创建时间

    owner = relationship("User", back_populates="feishu_apps")
    platform_accounts = relationship("PlatformAccount", back_populates="feishu_app")
    feishu_tables = relationship("FeishuTable", back_populates="feishu_app")



class PlatformAccount(Base):
    __tablename__ = "platform_accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    platform = Column(String(50))  # wechat_mp, wechat_service, xiaohongshu
    user_id = Column(Integer, ForeignKey("users.id"))
    login_status = Column(Boolean, default=False)
    last_login_time = Column(DateTime)
    cookies = Column(Text)  # 存储登录cookie
    created_at = Column(DateTime, default=datetime.utcnow)

    # 飞书应用关联
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用

    owner = relationship("User", back_populates="accounts")
    data_records = relationship("DataRecord", back_populates="account")
    feishu_tables = relationship("FeishuTable", back_populates="account")
    feishu_app = relationship("FeishuApp", back_populates="platform_accounts")

class DataRecord(Base):
    __tablename__ = "data_records"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    data_type = Column(String(50))  # user_summary, article_summary, etc.
    date = Column(DateTime)
    data = Column(JSON)  # 存储JSON格式的数据
    created_at = Column(DateTime, default=datetime.utcnow)

    account = relationship("PlatformAccount", back_populates="data_records")

class FeishuTable(Base):
    __tablename__ = "feishu_tables"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用
    feishu_table_id = Column(String(255))   # 数据表ID
    feishu_table_name = Column(String(255)) # 数据表名称
    data_type = Column(String(50))          # 数据类型：content_trend, content_source, content_detail, user_channel
    feishu_record_ids = Column(JSON)        # 该表的记录ID列表
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount", back_populates="feishu_tables")
    feishu_app = relationship("FeishuApp", back_populates="feishu_tables")