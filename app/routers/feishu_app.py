from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import logging
from typing import List, Optional
from pydantic import BaseModel

from app.database import get_db
from app.models import FeishuApp, User
from app.services.feishu_service import FeishuService
from app.routers.auth import get_current_user

router = APIRouter(prefix="/api/feishu-apps", tags=["飞书应用管理"])
logger = logging.getLogger(__name__)

# Pydantic模型
class FeishuAppCreate(BaseModel):
    name: str
    app_id: str
    app_secret: str

class FeishuAppUpdate(BaseModel):
    name: Optional[str] = None
    app_id: Optional[str] = None
    app_secret: Optional[str] = None

class FeishuAppResponse(BaseModel):
    id: int
    name: str
    app_id: str
    user_id: int
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    # 多维表格信息
    bitable_name: Optional[str] = None
    app_token: Optional[str] = None
    folder_token: Optional[str] = None
    url: Optional[str] = None
    bitable_created_at: Optional[datetime] = None
    has_bitable: bool = False

    class Config:
        from_attributes = True

class BitableCreateRequest(BaseModel):
    bitable_name: Optional[str] = None
    folder_token: Optional[str] = None

@router.get("/", response_model=List[FeishuAppResponse])
async def get_feishu_apps(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的飞书应用列表"""
    try:
        apps = db.query(FeishuApp).filter(
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).all()
        
        result = []
        for app in apps:
            app_data = FeishuAppResponse.model_validate(app)
            app_data.has_bitable = bool(app.app_token)
            result.append(app_data)
        
        return result
        
    except Exception as e:
        logger.error(f"获取飞书应用列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取飞书应用列表失败"
        )

@router.post("/", response_model=FeishuAppResponse)
async def create_feishu_app(
    app_data: FeishuAppCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新的飞书应用"""
    try:
        # 检查应用名称是否已存在
        existing_app = db.query(FeishuApp).filter(
            FeishuApp.user_id == current_user.id,
            FeishuApp.name == app_data.name,
            FeishuApp.is_deleted == False
        ).first()
        
        if existing_app:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="应用名称已存在"
            )
        
        # 验证飞书应用配置
        try:
            feishu_service = FeishuService(app_data.app_id, app_data.app_secret)
            # 简单验证：尝试获取access_token
            if not feishu_service.client:
                raise ValueError("飞书应用配置无效")
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"飞书应用配置验证失败: {str(e)}"
            )
        
        # 创建飞书应用
        feishu_app = FeishuApp(
            name=app_data.name,
            app_id=app_data.app_id,
            app_secret=app_data.app_secret,
            user_id=current_user.id
        )
        
        db.add(feishu_app)
        db.commit()
        db.refresh(feishu_app)
        
        logger.info(f"用户{current_user.id}创建飞书应用: {feishu_app.id}")
        
        result = FeishuAppResponse.model_validate(feishu_app)
        result.has_bitable = False
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞书应用失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建飞书应用失败: {str(e)}"
        )

@router.get("/with-bitable", response_model=List[FeishuAppResponse])
async def get_feishu_apps_with_bitable(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取已创建多维表格的飞书应用列表"""
    try:
        apps = db.query(FeishuApp).filter(
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False,
            FeishuApp.app_token.isnot(None),
            FeishuApp.app_token != ""
        ).all()

        result = []
        for app in apps:
            app_data = FeishuAppResponse.model_validate(app)
            app_data.has_bitable = True
            result.append(app_data)

        return result

    except Exception as e:
        logger.error(f"获取已创建多维表格的飞书应用列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取飞书应用列表失败"
        )

@router.get("/{app_id}", response_model=FeishuAppResponse)
async def get_feishu_app(
    app_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定飞书应用详情"""
    try:
        app = db.query(FeishuApp).filter(
            FeishuApp.id == app_id,
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).first()
        
        if not app:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞书应用不存在或无权限访问"
            )
        
        result = FeishuAppResponse.model_validate(app)
        result.has_bitable = bool(app.app_token)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取飞书应用详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取飞书应用详情失败"
        )

@router.put("/{app_id}", response_model=FeishuAppResponse)
async def update_feishu_app(
    app_id: int,
    app_data: FeishuAppUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新飞书应用信息"""
    try:
        app = db.query(FeishuApp).filter(
            FeishuApp.id == app_id,
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).first()
        
        if not app:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞书应用不存在或无权限访问"
            )
        
        # 检查名称是否重复
        if app_data.name and app_data.name != app.name:
            existing_app = db.query(FeishuApp).filter(
                FeishuApp.user_id == current_user.id,
                FeishuApp.name == app_data.name,
                FeishuApp.is_deleted == False,
                FeishuApp.id != app_id
            ).first()
            
            if existing_app:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="应用名称已存在"
                )
        
        # 验证新的飞书配置（如果提供）
        if app_data.app_id or app_data.app_secret:
            new_app_id = app_data.app_id or app.app_id
            new_app_secret = app_data.app_secret or app.app_secret
            
            try:
                feishu_service = FeishuService(new_app_id, new_app_secret)
                if not feishu_service.client:
                    raise ValueError("飞书应用配置无效")
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"飞书应用配置验证失败: {str(e)}"
                )
        
        # 更新字段
        if app_data.name is not None:
            app.name = app_data.name
        if app_data.app_id is not None:
            app.app_id = app_data.app_id
        if app_data.app_secret is not None:
            app.app_secret = app_data.app_secret
        
        app.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(app)
        
        logger.info(f"用户{current_user.id}更新飞书应用: {app.id}")
        
        result = FeishuAppResponse.model_validate(app)
        result.has_bitable = bool(app.app_token)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新飞书应用失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新飞书应用失败: {str(e)}"
        )

@router.delete("/{app_id}")
async def delete_feishu_app(
    app_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除飞书应用（软删除）"""
    try:
        app = db.query(FeishuApp).filter(
            FeishuApp.id == app_id,
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).first()
        
        if not app:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞书应用不存在或无权限访问"
            )
        
        # 软删除
        app.is_deleted = True
        app.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        
        logger.info(f"用户{current_user.id}删除飞书应用: {app.id}")
        
        return {"message": "飞书应用已删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除飞书应用失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除飞书应用失败: {str(e)}"
        )

@router.post("/{app_id}/create-bitable")
async def create_bitable_for_app(
    app_id: int,
    bitable_data: BitableCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """为飞书应用创建多维表格"""
    try:
        app = db.query(FeishuApp).filter(
            FeishuApp.id == app_id,
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).first()

        if not app:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞书应用不存在或无权限访问"
            )

        # 检查是否已创建多维表格
        if app.app_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该飞书应用已创建多维表格"
            )

        # 初始化飞书服务
        feishu_service = FeishuService(app.app_id, app.app_secret)

        # 创建多维表格
        bitable_name = bitable_data.bitable_name or f"{app.name}_数据表"

        bitable_result = feishu_service.create_bitable(
            name=bitable_name,
            folder_token=bitable_data.folder_token
        )

        if not bitable_result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建多维表格失败"
            )

        app_token = bitable_result.get('app_token')
        url = bitable_result.get('url')
        folder_token_result = bitable_result.get('folder_token')

        if not app_token:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建多维表格失败：未获取到app_token"
            )

        # 更新飞书应用记录
        app.bitable_name = bitable_name
        app.app_token = app_token
        app.folder_token = folder_token_result
        app.url = url
        app.bitable_created_at = datetime.now(timezone.utc)
        app.updated_at = datetime.now(timezone.utc)

        db.commit()

        logger.info(f"为飞书应用{app_id}创建多维表格成功: {app_token}")

        return {
            "success": True,
            "message": "多维表格创建成功",
            "app_token": app_token,
            "folder_token": folder_token_result,
            "url": url,
            "bitable_name": bitable_name
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"为飞书应用创建多维表格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建多维表格失败: {str(e)}"
        )


