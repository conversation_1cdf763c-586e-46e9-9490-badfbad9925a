from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import logging
from typing import Optional

from app.database import get_db
from app.models import PlatformAccount, User, FeishuTable, FeishuApp
from app.services.feishu_service import FeishuService
from app.services.data_converter import WeChatDataConverter
from app.services.wechat_service import WeChatMPService
from app.routers.auth import get_current_user

router = APIRouter(prefix="/api/feishu", tags=["feishu"])
logger = logging.getLogger(__name__)

@router.post("/bind-app/{account_id}")
async def bind_feishu_app_to_account(
    account_id: int,
    feishu_app_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """为账号绑定已有的飞书应用并创建数据表"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        # 查找飞书应用
        feishu_app = db.query(FeishuApp).filter(
            FeishuApp.id == feishu_app_id,
            FeishuApp.user_id == current_user.id,
            FeishuApp.is_deleted == False
        ).first()

        if not feishu_app:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞书应用不存在或无权限访问"
            )

        # 检查飞书应用是否已创建多维表格
        if not feishu_app.app_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该飞书应用尚未创建多维表格"
            )

        # 如果账号已绑定其他飞书应用，先删除已有的数据表
        if account.feishu_app_id:
            # 删除已有的数据表
            existing_tables = db.query(FeishuTable).filter(
                FeishuTable.account_id == account_id
            ).all()

            for table in existing_tables:
                db.delete(table)

        # 绑定新的飞书应用
        account.feishu_app_id = feishu_app_id

        # 初始化飞书服务
        feishu_service = FeishuService(feishu_app.app_id, feishu_app.app_secret)

        app_token = feishu_app.app_token
        
        # 获取微信数据类型配置
        from app.services.wechat_service import WeChatMPService
        data_types = WeChatMPService.DOWNLOAD_TEMPLATES

        created_tables = {}

        # 为每种数据类型创建表格
        for data_type, config in data_types.items():
            base_table_name = config.get('name', data_type)
            # 组合账号名称和表格名称
            table_name = f"{account.name}-{base_table_name}"

            # 创建数据表（包含字段）
            table_id = feishu_service.create_table_with_fields(app_token, table_name, data_type)
            if not table_id:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"创建{table_name}失败"
                )

            created_tables[data_type] = {
                "table_id": table_id,
                "table_name": table_name,
                "base_name": base_table_name
            }

            # 在数据库中记录表格信息
            feishu_table = FeishuTable(
                account_id=account_id,
                feishu_app_id=feishu_app_id,
                feishu_table_id=table_id,
                feishu_table_name=table_name,
                data_type=data_type,
                feishu_record_ids=[]
            )
            db.add(feishu_table)

        # 提交所有更改
        db.commit()

        logger.info(f"成功为账号{account_id}绑定飞书应用{feishu_app_id}并创建数据表")

        return {
            "success": True,
            "message": "飞书应用绑定成功，数据表创建完成",
            "app_token": app_token,
            "folder_token": feishu_app.folder_token,
            "url": feishu_app.url,
            "tables": created_tables,
            "bitable_name": feishu_app.bitable_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞书多维表格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建飞书多维表格失败: {str(e)}"
        )

@router.post("/sync-data/{account_id}")
async def sync_wechat_data_to_feishu(
    account_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """同步微信公众号数据到飞书多维表格"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        # 检查是否已绑定飞书应用
        if not account.feishu_app_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先绑定飞书应用"
            )

        # 获取飞书应用信息
        feishu_app = db.query(FeishuApp).filter(
            FeishuApp.id == account.feishu_app_id,
            FeishuApp.is_deleted == False
        ).first()

        if not feishu_app or not feishu_app.app_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="飞书应用不存在或未创建多维表格"
            )

        # 导入Excel解析工具
        from app.utils.excel_parser import ExcelDataParser
        parser = ExcelDataParser()

        # 获取存储目录
        storage_dir = parser.get_storage_directory(account.feishu_app_id, account_id)

        # 检查存储目录是否存在Excel文件
        excel_files = parser.list_excel_files(storage_dir)
        if not excel_files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未找到已下载的Excel文件，请先下载数据"
            )

        # 获取文件与数据类型的映射
        file_mapping = parser.get_file_data_type_mapping(storage_dir)
        if not file_mapping:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法识别Excel文件的数据类型"
            )

        # 初始化飞书服务
        feishu_service = FeishuService(feishu_app.app_id, feishu_app.app_secret)

        # 获取账号的飞书表格信息
        feishu_tables = db.query(FeishuTable).filter(
            FeishuTable.account_id == account_id
        ).all()

        if not feishu_tables:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未找到飞书表格配置，请先创建飞书多维表格"
            )

        sync_results = {
            "synced_tables": {},
            "errors": [],
            "user_data_synced": 0,
            "article_data_synced": 0
        }

        # 创建飞书表格映射（data_type -> FeishuTable）
        table_mapping = {}
        for table in feishu_tables:
            table_mapping[table.data_type] = table

        # 处理每个Excel文件
        total_synced = 0
        for filename, data_type in file_mapping.items():
            try:
                # 检查是否有对应的飞书表格
                if data_type not in table_mapping:
                    error_msg = f"数据类型 {data_type} 没有对应的飞书表格"
                    sync_results["errors"].append(error_msg)
                    logger.warning(error_msg)
                    continue

                feishu_table = table_mapping[data_type]
                file_path = f"{storage_dir}/{filename}"

                # 解析Excel数据
                records = parser.parse_excel_data(file_path, data_type)
                if not records:
                    error_msg = f"文件 {filename} 解析失败或无数据"
                    sync_results["errors"].append(error_msg)
                    logger.warning(error_msg)
                    continue

                # 检查并删除已有记录
                existing_record_ids = feishu_table.feishu_record_ids or []
                if existing_record_ids:
                    logger.info(f"删除表格 {feishu_table.feishu_table_name} 中的 {len(existing_record_ids)} 条已有记录")
                    if not feishu_service.batch_delete_records(
                        feishu_app.app_token,
                        feishu_table.feishu_table_id,
                        existing_record_ids
                    ):
                        error_msg = f"删除表格 {feishu_table.feishu_table_name} 已有记录失败"
                        sync_results["errors"].append(error_msg)
                        logger.error(error_msg)
                        continue

                # 批量创建新记录
                new_record_ids = feishu_service.batch_create_records(
                    feishu_app.app_token,
                    feishu_table.feishu_table_id,
                    records
                )

                if new_record_ids:
                    # 更新数据库中的记录ID
                    feishu_table.feishu_record_ids = new_record_ids
                    db.commit()

                    sync_results["synced_tables"][data_type] = {
                        "table_name": feishu_table.feishu_table_name,
                        "records_synced": len(new_record_ids),
                        "filename": filename
                    }

                    total_synced += len(new_record_ids)

                    # 根据数据类型统计
                    if data_type in ['user_channel']:
                        sync_results["user_data_synced"] += len(new_record_ids)
                    else:
                        sync_results["article_data_synced"] += len(new_record_ids)

                    logger.info(f"成功同步 {data_type} 数据: {len(new_record_ids)} 条记录")
                else:
                    error_msg = f"创建 {data_type} 记录失败"
                    sync_results["errors"].append(error_msg)
                    logger.error(error_msg)

            except Exception as e:
                error_msg = f"处理文件 {filename} 时发生错误: {str(e)}"
                sync_results["errors"].append(error_msg)
                logger.error(error_msg)

        logger.info(f"账号{account_id}数据同步完成: 总计同步 {total_synced} 条记录")

        return {
            "success": True,
            "message": f"数据同步完成，共同步 {total_synced} 条记录",
            "sync_results": sync_results,
            "data_summary": {
                "total_records": total_synced,
                "files_processed": len(file_mapping),
                "errors_count": len(sync_results["errors"])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步数据到飞书失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步数据失败: {str(e)}"
        )

@router.get("/bitable-info/{account_id}")
async def get_bitable_info(
    account_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取账号的飞书多维表格信息"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        if not account.feishu_app_id:
            return {
                "success": True,
                "has_bitable": False,
                "message": "尚未绑定飞书应用"
            }

        # 获取飞书应用信息
        feishu_app = db.query(FeishuApp).filter(
            FeishuApp.id == account.feishu_app_id,
            FeishuApp.is_deleted == False
        ).first()

        if not feishu_app:
            return {
                "success": True,
                "has_bitable": False,
                "message": "飞书应用不存在"
            }

        if not feishu_app.app_token:
            return {
                "success": True,
                "has_bitable": False,
                "message": "飞书应用尚未创建多维表格"
            }

        # 获取飞书表格信息
        feishu_tables = db.query(FeishuTable).filter(
            FeishuTable.account_id == account_id
        ).all()

        tables_info = {}
        total_records = 0

        for table in feishu_tables:
            record_ids = table.feishu_record_ids or []
            record_count = len(record_ids) if isinstance(record_ids, list) else 0
            total_records += record_count

            tables_info[table.data_type] = {
                "table_id": table.feishu_table_id,
                "table_name": table.feishu_table_name,
                "record_count": record_count,
                "created_at": table.created_at.isoformat() if table.created_at else None,
                "updated_at": table.updated_at.isoformat() if table.updated_at else None
            }

        return {
            "success": True,
            "has_bitable": True,
            "app_token": feishu_app.app_token,
            "folder_token": feishu_app.folder_token,
            "url": feishu_app.url,
            "bitable_name": feishu_app.bitable_name,
            "feishu_app_name": feishu_app.name,
            "tables": tables_info,
            "total_record_count": total_records,
            "created_at": account.feishu_created_at.isoformat() if account.feishu_created_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取飞书表格信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取飞书表格信息失败: {str(e)}"
        )
