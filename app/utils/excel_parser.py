import pandas as pd
import os
import re
from typing import Dict, List, Any, Optional, Tuple
import logging
from app.services.wechat_service import WeChatMPService

logger = logging.getLogger(__name__)

class ExcelDataParser:
    """Excel数据解析工具类"""
    
    def __init__(self):
        self.templates = WeChatMPService.DOWNLOAD_TEMPLATES
    
    def parse_filename_to_data_type(self, filename: str) -> Optional[str]:
        """根据文件名解析数据类型
        
        Args:
            filename: Excel文件名
            
        Returns:
            数据类型，如果无法识别返回None
        """
        # 文件名格式：wechat_data_account_{account_id}_{data_type}_{start_date}_to_{end_date}.xlsx
        # 使用更精确的正则表达式，匹配日期格式来确定数据类型的边界
        pattern = r'wechat_data_account_\d+_(.+)_\d{4}-\d{2}-\d{2}_to_\d{4}-\d{2}-\d{2}\.xlsx'
        match = re.search(pattern, filename)

        if match:
            data_type = match.group(1)
            if data_type in self.templates:
                return data_type
        
        logger.warning(f"无法从文件名解析数据类型: {filename}")
        return None
    
    def get_storage_directory(self, feishu_app_id: int, account_id: int) -> str:
        """获取存储目录路径

        Args:
            feishu_app_id: 飞书应用ID
            account_id: 账号ID

        Returns:
            存储目录路径
        """
        # 使用项目根目录下的data文件夹
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        return os.path.join(project_root, "data", f"feishu_app_{feishu_app_id}", f"account_{account_id}")
    
    def ensure_directory_exists(self, directory: str) -> bool:
        """确保目录存在
        
        Args:
            directory: 目录路径
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {directory}, 错误: {e}")
            return False
    
    def clear_directory(self, directory: str) -> bool:
        """清理目录中的所有文件
        
        Args:
            directory: 目录路径
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            if not os.path.exists(directory):
                return True
                
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    logger.info(f"删除文件: {file_path}")
            
            return True
        except Exception as e:
            logger.error(f"清理目录失败: {directory}, 错误: {e}")
            return False
    
    def save_excel_file(self, directory: str, filename: str, data: bytes) -> bool:
        """保存Excel文件到指定目录
        
        Args:
            directory: 目录路径
            filename: 文件名
            data: 文件数据
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            if not self.ensure_directory_exists(directory):
                return False
            
            file_path = os.path.join(directory, filename)
            with open(file_path, 'wb') as f:
                f.write(data)
            
            logger.info(f"保存Excel文件成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存Excel文件失败: {filename}, 错误: {e}")
            return False
    
    def list_excel_files(self, directory: str) -> List[str]:
        """列出目录中的所有Excel文件
        
        Args:
            directory: 目录路径
            
        Returns:
            Excel文件名列表
        """
        try:
            if not os.path.exists(directory):
                return []
            
            excel_files = []
            for filename in os.listdir(directory):
                if filename.endswith('.xlsx') or filename.endswith('.xls'):
                    excel_files.append(filename)
            
            return excel_files
        except Exception as e:
            logger.error(f"列出Excel文件失败: {directory}, 错误: {e}")
            return []
    
    def parse_excel_data(self, file_path: str, data_type: str) -> Optional[List[Dict[str, Any]]]:
        """解析Excel文件数据
        
        Args:
            file_path: Excel文件路径
            data_type: 数据类型
            
        Returns:
            解析后的数据列表，失败返回None
        """
        try:
            if data_type not in self.templates:
                logger.error(f"未知的数据类型: {data_type}")
                return None
            
            template = self.templates[data_type]
            data_start_row = template.get('data_start_row', 2)  # 默认从第2行开始
            fields = template.get('fields', [])
            
            # 读取Excel文件，处理不同格式
            try:
                # 首先尝试检查文件是否为HTML格式的Excel
                is_html = False
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read(1000)  # 读取前1000个字符
                        if '<html' in content.lower() or '<table' in content.lower():
                            is_html = True
                except UnicodeDecodeError:
                    # 如果UTF-8解码失败，说明不是HTML文件
                    is_html = False

                if is_html:
                    # 这是HTML格式的Excel文件，使用pandas的read_html
                    logger.info(f"检测到HTML格式的Excel文件: {file_path}")
                    tables = pd.read_html(file_path, header=[0, 1, 2], encoding='utf-8')
                    if tables:
                        df = tables[0]  # 取第一个表格
                        # 处理多级列标题的HTML表格
                        df = self._process_html_table(df, data_type)
                    else:
                        raise ValueError("HTML文件中没有找到表格")
                else:
                    # 标准Excel文件，使用openpyxl引擎
                    logger.info(f"检测到标准Excel文件: {file_path}")
                    df = pd.read_excel(file_path, header=None, engine='openpyxl')
            except Exception as e:
                logger.warning(f"使用主要方法失败: {e}，尝试其他方法")
                try:
                    # 如果失败，尝试不同的引擎和编码
                    # 先尝试xlrd引擎
                    df = pd.read_excel(file_path, header=None, engine='xlrd')
                except Exception as e2:
                    try:
                        # 最后尝试calamine引擎（如果可用）
                        df = pd.read_excel(file_path, header=None, engine='calamine')
                    except Exception as e3:
                        logger.error(f"所有方法都失败: openpyxl={e}, xlrd={e2}, calamine={e3}")
                        raise e3
            
            if df.empty:
                logger.warning(f"Excel文件为空: {file_path}")
                return []
            
            # 从指定行开始读取数据（pandas行索引从0开始，所以减1）
            data_rows = df.iloc[data_start_row - 1:]
            
            if data_rows.empty:
                logger.warning(f"没有数据行: {file_path}")
                return []
            
            # 转换数据格式
            records = []
            for _, row in data_rows.iterrows():
                record = {}
                for i, (field_name, field_type) in enumerate(fields):
                    if i < len(row):
                        value = row.iloc[i]
                        # 处理NaN值
                        if pd.isna(value):
                            value = None
                        elif field_type == 2:  # 数字类型
                            try:
                                value = float(value) if value is not None else 0
                            except (ValueError, TypeError):
                                value = 0
                        else:  # 文本类型
                            value = str(value) if value is not None else ""
                        
                        record[field_name] = value
                
                # 只添加非空记录
                if any(v for v in record.values() if v not in [None, "", 0]):
                    records.append(record)
            
            logger.info(f"成功解析Excel文件: {file_path}, 记录数: {len(records)}")
            return records
            
        except Exception as e:
            logger.error(f"解析Excel文件失败: {file_path}, 错误: {e}")
            return None
    
    def get_file_data_type_mapping(self, directory: str) -> Dict[str, str]:
        """获取目录中文件与数据类型的映射
        
        Args:
            directory: 目录路径
            
        Returns:
            文件名到数据类型的映射字典
        """
        mapping = {}
        excel_files = self.list_excel_files(directory)
        
        for filename in excel_files:
            data_type = self.parse_filename_to_data_type(filename)
            if data_type:
                mapping[filename] = data_type
        
        return mapping

    def _process_html_table(self, df: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """处理HTML格式的多级表格

        Args:
            df: 原始DataFrame
            data_type: 数据类型

        Returns:
            处理后的DataFrame
        """
        try:
            if data_type == 'user_channel':
                # 用户渠道数据的特殊处理
                # 数据实际在列标题中，需要转置
                if df.empty and hasattr(df, 'columns'):
                    # 从多级列标题中提取数据
                    columns = df.columns
                    if len(columns) > 0 and hasattr(columns, 'levels'):
                        # 多级列标题
                        data_rows = []

                        # 提取时间行（第一级标题中的日期）
                        time_row = []
                        for col in columns:
                            if len(col) >= 4 and col[3] and str(col[3]).startswith('2025-'):
                                time_row.append(col[3])

                        if time_row:
                            # 提取各个指标的数据行
                            metrics = ['新关注人数', '取消关注人数', '净增关注人数', '累积关注人数']
                            for metric in metrics:
                                metric_row = [metric]  # 第一列是指标名称
                                for col in columns:
                                    if len(col) >= 2 and metric in str(col[1]):
                                        # 找到对应指标的数据
                                        for i in range(3, len(col)):
                                            if col[i] is not None and str(col[i]).replace('-', '').isdigit():
                                                metric_row.append(col[i])
                                                break
                                        else:
                                            metric_row.append(0)

                                if len(metric_row) > 1:
                                    data_rows.append(metric_row)

                        if data_rows:
                            # 创建新的DataFrame
                            max_cols = max(len(row) for row in data_rows)
                            # 补齐列数
                            for row in data_rows:
                                while len(row) < max_cols:
                                    row.append(0)

                            df = pd.DataFrame(data_rows)
                            logger.info(f"HTML表格处理成功，转换为 {df.shape} 的DataFrame")

            return df

        except Exception as e:
            logger.warning(f"HTML表格处理失败: {e}，返回原始DataFrame")
            return df
