import pandas as pd
import os
import re
from typing import Dict, List, Any, Optional, Tuple
import logging
from app.services.wechat_service import WeChatMPService

logger = logging.getLogger(__name__)

class ExcelDataParser:
    """Excel数据解析工具类"""
    
    def __init__(self):
        self.templates = WeChatMPService.DOWNLOAD_TEMPLATES
    
    def parse_filename_to_data_type(self, filename: str) -> Optional[str]:
        """根据文件名解析数据类型
        
        Args:
            filename: Excel文件名
            
        Returns:
            数据类型，如果无法识别返回None
        """
        # 文件名格式：wechat_data_account_{account_id}_{data_type}_{start_date}_to_{end_date}.xlsx
        pattern = r'wechat_data_account_\d+_([^_]+)_\d{4}-\d{2}-\d{2}_to_\d{4}-\d{2}-\d{2}\.xlsx'
        match = re.search(pattern, filename)
        
        if match:
            data_type = match.group(1)
            if data_type in self.templates:
                return data_type
        
        logger.warning(f"无法从文件名解析数据类型: {filename}")
        return None
    
    def get_storage_directory(self, feishu_app_id: int, account_id: int) -> str:
        """获取存储目录路径
        
        Args:
            feishu_app_id: 飞书应用ID
            account_id: 账号ID
            
        Returns:
            存储目录路径
        """
        return f"/data/feishu_app_{feishu_app_id}/account_{account_id}"
    
    def ensure_directory_exists(self, directory: str) -> bool:
        """确保目录存在
        
        Args:
            directory: 目录路径
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {directory}, 错误: {e}")
            return False
    
    def clear_directory(self, directory: str) -> bool:
        """清理目录中的所有文件
        
        Args:
            directory: 目录路径
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            if not os.path.exists(directory):
                return True
                
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    logger.info(f"删除文件: {file_path}")
            
            return True
        except Exception as e:
            logger.error(f"清理目录失败: {directory}, 错误: {e}")
            return False
    
    def save_excel_file(self, directory: str, filename: str, data: bytes) -> bool:
        """保存Excel文件到指定目录
        
        Args:
            directory: 目录路径
            filename: 文件名
            data: 文件数据
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            if not self.ensure_directory_exists(directory):
                return False
            
            file_path = os.path.join(directory, filename)
            with open(file_path, 'wb') as f:
                f.write(data)
            
            logger.info(f"保存Excel文件成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存Excel文件失败: {filename}, 错误: {e}")
            return False
    
    def list_excel_files(self, directory: str) -> List[str]:
        """列出目录中的所有Excel文件
        
        Args:
            directory: 目录路径
            
        Returns:
            Excel文件名列表
        """
        try:
            if not os.path.exists(directory):
                return []
            
            excel_files = []
            for filename in os.listdir(directory):
                if filename.endswith('.xlsx') or filename.endswith('.xls'):
                    excel_files.append(filename)
            
            return excel_files
        except Exception as e:
            logger.error(f"列出Excel文件失败: {directory}, 错误: {e}")
            return []
    
    def parse_excel_data(self, file_path: str, data_type: str) -> Optional[List[Dict[str, Any]]]:
        """解析Excel文件数据
        
        Args:
            file_path: Excel文件路径
            data_type: 数据类型
            
        Returns:
            解析后的数据列表，失败返回None
        """
        try:
            if data_type not in self.templates:
                logger.error(f"未知的数据类型: {data_type}")
                return None
            
            template = self.templates[data_type]
            data_start_row = template.get('data_start_row', 2)  # 默认从第2行开始
            fields = template.get('fields', [])
            
            # 读取Excel文件
            df = pd.read_excel(file_path, header=None)
            
            if df.empty:
                logger.warning(f"Excel文件为空: {file_path}")
                return []
            
            # 从指定行开始读取数据（pandas行索引从0开始，所以减1）
            data_rows = df.iloc[data_start_row - 1:]
            
            if data_rows.empty:
                logger.warning(f"没有数据行: {file_path}")
                return []
            
            # 转换数据格式
            records = []
            for _, row in data_rows.iterrows():
                record = {}
                for i, (field_name, field_type) in enumerate(fields):
                    if i < len(row):
                        value = row.iloc[i]
                        # 处理NaN值
                        if pd.isna(value):
                            value = None
                        elif field_type == 2:  # 数字类型
                            try:
                                value = float(value) if value is not None else 0
                            except (ValueError, TypeError):
                                value = 0
                        else:  # 文本类型
                            value = str(value) if value is not None else ""
                        
                        record[field_name] = value
                
                # 只添加非空记录
                if any(v for v in record.values() if v not in [None, "", 0]):
                    records.append(record)
            
            logger.info(f"成功解析Excel文件: {file_path}, 记录数: {len(records)}")
            return records
            
        except Exception as e:
            logger.error(f"解析Excel文件失败: {file_path}, 错误: {e}")
            return None
    
    def get_file_data_type_mapping(self, directory: str) -> Dict[str, str]:
        """获取目录中文件与数据类型的映射
        
        Args:
            directory: 目录路径
            
        Returns:
            文件名到数据类型的映射字典
        """
        mapping = {}
        excel_files = self.list_excel_files(directory)
        
        for filename in excel_files:
            data_type = self.parse_filename_to_data_type(filename)
            if data_type:
                mapping[filename] = data_type
        
        return mapping
