# 飞书多维表格集成文档

## 概述

本项目已集成飞书多维表格功能，可以将微信公众号的数据自动同步到飞书多维表格中，方便团队协作和数据分析。

## 功能特性

- ✅ 创建飞书多维表格
- ✅ 自动创建用户概况和图文分析数据表
- ✅ 自动创建表格字段（根据数据类型）
- ✅ 解析微信公众号Excel数据
- ✅ 批量写入数据到飞书表格
- ✅ 支持数据更新和增量同步
- ✅ 完整的错误处理和日志记录

## 配置要求

### 1. 飞书应用配置

1. 登录[飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取应用凭证：
   - App ID
   - App Secret
4. 配置应用权限：
   - `bitable:app` - 多维表格应用权限
   - `bitable:app:readonly` - 多维表格应用只读权限

### 2. 环境变量配置

在项目根目录的 `.env` 文件中添加：

```bash
# 飞书配置
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret
```

### 3. 安装依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装新增依赖
pip install lark-oapi==1.4.19 pandas==2.1.4
```

### 4. 数据库迁移

运行迁移脚本添加飞书相关字段：

```bash
python migrate_add_feishu_fields.py
```

## API接口

### 1. 创建多维表格

**接口**: `POST /api/feishu/create-bitable/{account_id}`

**参数**:
- `account_id`: 微信公众号账号ID
- `bitable_name`: 表格名称（可选）
- `folder_token`: 存放目录token（可选）

**响应示例**:
```json
{
  "success": true,
  "message": "飞书多维表格创建成功",
  "app_token": "bascnCMII2ORej2RItqpZZUNMIe",
  "user_table_id": "tblsRc9GRRXKqhvW",
  "article_table_id": "tblDpt8mKdkjuOWt",
  "bitable_name": "公众号名称_数据分析表格"
}
```

### 2. 同步数据到飞书

**接口**: `POST /api/feishu/sync-data/{account_id}`

**参数**:
- `account_id`: 微信公众号账号ID
- `begin_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应示例**:
```json
{
  "success": true,
  "message": "数据同步完成",
  "sync_results": {
    "user_data_synced": 30,
    "article_data_synced": 15,
    "errors": []
  },
  "data_summary": {
    "user_records": 30,
    "article_records": 15,
    "sheets_found": ["用户概况", "图文分析"]
  }
}
```

### 3. 获取表格信息

**接口**: `GET /api/feishu/bitable-info/{account_id}`

**响应示例**:
```json
{
  "success": true,
  "has_bitable": true,
  "app_token": "bascnCMII2ORej2RItqpZZUNMIe",
  "user_table_id": "tblsRc9GRRXKqhvW",
  "article_table_id": "tblDpt8mKdkjuOWt",
  "record_count": 45,
  "created_at": "2024-01-15T10:30:00"
}
```

## 使用流程

### 1. 完整使用流程

```python
# 1. 确保微信公众号已登录
# 2. 为账号创建飞书多维表格
POST /api/feishu/create-bitable/1

# 3. 同步数据到飞书表格
POST /api/feishu/sync-data/1
{
  "begin_date": "2024-01-01",
  "end_date": "2024-01-31"
}

# 4. 查看表格信息
GET /api/feishu/bitable-info/1
```

### 2. 数据表结构

系统会自动创建以下字段结构：

#### 用户概况数据表
| 字段名 | 飞书字段类型 | 说明 |
|--------|-------------|------|
| 日期 | 多行文本(1) | 统计日期 |
| 新增用户数 | 数字(2) | 当日新增关注用户数 |
| 取消关注用户数 | 数字(2) | 当日取消关注用户数 |
| 净增长 | 数字(2) | 净增长用户数 |
| 累计用户数 | 数字(2) | 累计关注用户数 |

#### 图文分析数据表
| 字段名 | 飞书字段类型 | 说明 |
|--------|-------------|------|
| 文章标题 | 多行文本(1) | 文章标题 |
| 阅读数 | 数字(2) | 文章阅读数 |
| 点赞数 | 数字(2) | 文章点赞数 |
| 分享数 | 数字(2) | 文章分享数 |
| 发布时间 | 多行文本(1) | 文章发布时间 |
| 送达人数 | 数字(2) | 文章送达人数（如有） |

**注意**: 字段类型编号对应飞书API中的字段类型，系统会自动创建这些字段，无需手动配置。

## 测试

### 运行测试脚本

```bash
# 测试飞书服务功能
python test_feishu_service.py
```

测试内容包括：
- 数据转换器功能测试
- 飞书服务连接测试
- 多维表格创建测试
- 数据写入测试

## 注意事项

### 1. 权限要求
- 确保飞书应用有足够的权限创建和操作多维表格
- 用户需要在飞书中有相应的操作权限

### 2. 数据限制
- 飞书API单次最多支持500条记录的批量操作
- 大量数据会自动分批处理

### 3. 错误处理
- 网络异常会自动重试
- API调用失败会记录详细错误信息
- 数据格式错误会跳过并记录

### 4. 性能考虑
- 大量数据同步可能需要较长时间
- 建议在低峰期进行数据同步

## 故障排除

### 常见问题

1. **飞书应用凭证错误**
   - 检查 `FEISHU_APP_ID` 和 `FEISHU_APP_SECRET` 是否正确
   - 确认应用状态是否正常

2. **权限不足**
   - 检查飞书应用权限配置
   - 确认用户在飞书中的权限

3. **数据同步失败**
   - 检查微信公众号登录状态
   - 确认数据日期范围是否有效

4. **表格创建失败**
   - 检查网络连接
   - 确认飞书服务状态

### 日志查看

应用日志会记录详细的操作信息，包括：
- API调用状态
- 数据处理过程
- 错误详情

## 扩展功能

### 未来计划
- [ ] 支持更多数据类型同步
- [ ] 添加数据可视化图表
- [ ] 支持定时自动同步
- [ ] 添加数据对比和分析功能
- [ ] 支持多个飞书工作区

### 自定义开发
项目提供了完整的服务类，可以根据需要扩展：
- `FeishuService`: 飞书API操作
- `WeChatDataConverter`: 数据格式转换
- 自定义数据处理逻辑
