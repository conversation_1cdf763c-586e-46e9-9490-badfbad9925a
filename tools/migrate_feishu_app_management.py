#!/usr/bin/env python3
"""
数据库迁移脚本：飞书应用管理功能
1. 创建 feishu_apps 表
2. 修改 platform_accounts 表：删除旧飞书字段，新增 feishu_app_id
3. 修改 feishu_tables 表：feishu_app_token 改为 feishu_app_id
4. 数据迁移：将现有飞书数据迁移到新结构
"""

import os
import sys
from sqlalchemy import create_engine, text, inspect
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_database_url():
    """获取数据库URL"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        if os.path.exists("social_media.db"):
            database_url = "sqlite:///./social_media.db"
        else:
            database_url = "sqlite:///./social_media_manager.db"
    return database_url

def migrate_feishu_app_management():
    """执行飞书应用管理迁移"""
    print("🚀 开始飞书应用管理数据库迁移...")
    
    database_url = get_database_url()
    print(f"数据库URL: {database_url}")
    
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as connection:
            trans = connection.begin()
            
            try:
                # 检查表是否存在
                inspector = inspect(engine)
                existing_tables = inspector.get_table_names()
                
                if "platform_accounts" not in existing_tables:
                    print("❌ platform_accounts表不存在，请先运行主应用创建基础表结构")
                    return False
                
                # 1. 创建 feishu_apps 表
                print("📝 创建 feishu_apps 表...")
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS feishu_apps (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100),
                        app_id VARCHAR(255),
                        app_secret VARCHAR(255),
                        user_id INTEGER,
                        is_deleted BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        bitable_name VARCHAR(255),
                        app_token VARCHAR(255),
                        folder_token VARCHAR(255),
                        url VARCHAR(500),
                        bitable_created_at DATETIME,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                # 2. 检查 platform_accounts 表的现有字段
                columns = [col['name'] for col in inspector.get_columns('platform_accounts')]
                
                # 3. 检查是否有现有飞书数据需要迁移
                print("📊 检查现有飞书数据...")
                
                # 4. 先添加 feishu_app_id 字段到 platform_accounts（如果不存在）
                if 'feishu_app_id' not in columns:
                    print("📝 添加 feishu_app_id 字段到 platform_accounts 表...")
                    connection.execute(text("""
                        ALTER TABLE platform_accounts
                        ADD COLUMN feishu_app_id INTEGER REFERENCES feishu_apps(id)
                    """))

                # 5. 先添加 feishu_app_id 字段到 feishu_tables（如果存在该表）
                if 'feishu_tables' in existing_tables:
                    feishu_tables_columns = [col['name'] for col in inspector.get_columns('feishu_tables')]
                    if 'feishu_app_id' not in feishu_tables_columns:
                        print("📝 添加 feishu_app_id 字段到 feishu_tables 表...")
                        connection.execute(text("""
                            ALTER TABLE feishu_tables
                            ADD COLUMN feishu_app_id INTEGER REFERENCES feishu_apps(id)
                        """))

                # 6. 重新获取有飞书数据的账号（在添加字段后）
                if 'feishu_app_token' in columns:
                    print("📊 重新获取飞书数据进行迁移...")
                    result = connection.execute(text("""
                        SELECT DISTINCT user_id, feishu_app_token, feishu_folder_token,
                               feishu_url, feishu_created_at, name
                        FROM platform_accounts
                        WHERE feishu_app_token IS NOT NULL AND feishu_app_token != ''
                    """))

                    feishu_data = result.fetchall()

                    for row in feishu_data:
                        user_id, app_token, folder_token, url, created_at, account_name = row

                        # 检查是否已经为这个app_token创建了飞书应用
                        existing_app = connection.execute(text("""
                            SELECT id FROM feishu_apps WHERE app_token = :app_token
                        """), {'app_token': app_token}).fetchone()

                        if existing_app:
                            feishu_app_id = existing_app[0]
                        else:
                            # 创建飞书应用记录
                            app_name = f"{account_name}_飞书应用"
                            bitable_name = f"{account_name}_数据表"

                            # 插入飞书应用记录
                            result = connection.execute(text("""
                                INSERT INTO feishu_apps
                                (name, app_id, app_secret, user_id, bitable_name, app_token,
                                 folder_token, url, bitable_created_at, created_at, updated_at)
                                VALUES (:name, 'migrated_app_id', 'migrated_app_secret', :user_id,
                                        :bitable_name, :app_token, :folder_token, :url,
                                        :bitable_created_at, :created_at, :updated_at)
                            """), {
                                'name': app_name,
                                'user_id': user_id,
                                'bitable_name': bitable_name,
                                'app_token': app_token,
                                'folder_token': folder_token,
                                'url': url,
                                'bitable_created_at': created_at,
                                'created_at': datetime.utcnow(),
                                'updated_at': datetime.utcnow()
                            })

                            feishu_app_id = result.lastrowid

                        # 更新 platform_accounts 表，设置 feishu_app_id
                        connection.execute(text("""
                            UPDATE platform_accounts
                            SET feishu_app_id = :feishu_app_id
                            WHERE user_id = :user_id AND feishu_app_token = :app_token
                        """), {
                            'feishu_app_id': feishu_app_id,
                            'user_id': user_id,
                            'app_token': app_token
                        })

                        # 更新 feishu_tables 表
                        if 'feishu_tables' in existing_tables:
                            connection.execute(text("""
                                UPDATE feishu_tables
                                SET feishu_app_id = :feishu_app_id
                                WHERE feishu_app_token = :app_token
                            """), {
                                'feishu_app_id': feishu_app_id,
                                'app_token': app_token
                            })
                
                # 提交事务
                trans.commit()
                print("✅ 飞书应用管理迁移完成！")
                
                # 显示迁移结果
                result = connection.execute(text("SELECT COUNT(*) FROM feishu_apps"))
                app_count = result.fetchone()[0]
                print(f"📊 迁移结果：创建了 {app_count} 个飞书应用记录")
                
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ 迁移过程中出错: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def cleanup_old_fields():
    """清理旧字段（可选，建议在确认迁移成功后手动执行）"""
    print("\n⚠️  注意：以下是清理旧字段的SQL命令，请在确认迁移成功后手动执行：")
    print("""
    -- 删除 platform_accounts 表的旧飞书字段
    ALTER TABLE platform_accounts DROP COLUMN feishu_app_token;
    ALTER TABLE platform_accounts DROP COLUMN feishu_folder_token;
    ALTER TABLE platform_accounts DROP COLUMN feishu_url;
    ALTER TABLE platform_accounts DROP COLUMN feishu_created_at;
    
    -- 删除 feishu_tables 表的旧字段
    ALTER TABLE feishu_tables DROP COLUMN feishu_app_token;
    """)

if __name__ == "__main__":
    success = migrate_feishu_app_management()
    if success:
        cleanup_old_fields()
    else:
        print("❌ 迁移失败，请检查错误信息")
        sys.exit(1)
