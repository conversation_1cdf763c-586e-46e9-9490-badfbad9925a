#!/usr/bin/env python3
"""
数据库迁移脚本：为PlatformAccount表添加飞书相关字段
"""
import os
import sys
from sqlalchemy import text
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

from app.database import engine

def migrate_add_feishu_fields():
    """添加飞书相关字段到platform_accounts表"""
    print("开始数据库迁移：添加飞书相关字段")
    
    try:
        with engine.connect() as connection:
            # 检查表是否存在（SQLite语法）
            result = connection.execute(text("""
                SELECT COUNT(*) as count
                FROM sqlite_master
                WHERE type='table' AND name='platform_accounts'
            """))

            if result.fetchone()[0] == 0:
                print("❌ platform_accounts表不存在，请先运行主应用创建基础表结构")
                return False

            # 检查字段是否已存在（SQLite语法）
            existing_columns = []
            result = connection.execute(text("PRAGMA table_info(platform_accounts)"))

            for row in result:
                existing_columns.append(row[1])  # SQLite PRAGMA返回的第二列是字段名
            
            print(f"当前表字段: {existing_columns}")
            
            # 需要添加的字段
            fields_to_add = [
                ("feishu_app_token", "VARCHAR(255)"),
                ("feishu_table_id", "VARCHAR(255)"), 
                ("feishu_record_ids", "JSON"),
                ("feishu_created_at", "DATETIME")
            ]
            
            # 添加缺失的字段
            for field_name, field_type in fields_to_add:
                if field_name not in existing_columns:
                    print(f"添加字段: {field_name} ({field_type})")

                    # SQLite语法调整
                    if field_type == "JSON":
                        field_type = "TEXT"  # SQLite不支持JSON类型
                    elif field_type == "DATETIME":
                        field_type = "DATETIME"  # SQLite支持DATETIME

                    sql = f"ALTER TABLE platform_accounts ADD COLUMN {field_name} {field_type}"

                    connection.execute(text(sql))
                    print(f"✅ 字段 {field_name} 添加成功")
                else:
                    print(f"⏭️  字段 {field_name} 已存在，跳过")
            
            # 提交事务
            connection.commit()
            print("✅ 数据库迁移完成")
            return True
            
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_migration():
    """验证迁移是否成功"""
    print("\n验证迁移结果...")

    try:
        with engine.connect() as connection:
            # SQLite语法
            result = connection.execute(text("PRAGMA table_info(platform_accounts)"))

            print("platform_accounts表当前结构:")
            for row in result:
                print(f"  - {row[1]}: {row[2]}")  # name: type

            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("飞书字段数据库迁移工具")
    print("=" * 60)
    
    # 执行迁移
    migration_success = migrate_add_feishu_fields()
    
    if migration_success:
        # 验证迁移
        verify_migration()
        print("\n🎉 迁移成功完成！")
        return 0
    else:
        print("\n❌ 迁移失败！")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
