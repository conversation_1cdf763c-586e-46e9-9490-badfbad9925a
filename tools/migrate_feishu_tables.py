#!/usr/bin/env python3
"""
数据库迁移脚本：重构飞书表格相关字段
1. 创建 feishu_tables 表
2. 删除 platform_accounts 表中的 feishu_table_id, feishu_record_ids 字段
3. 添加 platform_accounts 表中的 feishu_folder_token, feishu_url 字段
"""

import os
import sys
from sqlalchemy import create_engine, text
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_database_url():
    """获取数据库URL"""
    # 从环境变量获取，如果没有则使用默认的SQLite
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        # 检查是否有现有的数据库文件
        if os.path.exists("social_media.db"):
            database_url = "sqlite:///./social_media.db"
        else:
            database_url = "sqlite:///./social_media_manager.db"
    return database_url

def migrate_feishu_tables():
    """执行飞书表格迁移"""
    print("🚀 开始飞书表格数据库迁移...")
    
    database_url = get_database_url()
    print(f"数据库URL: {database_url}")
    
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as connection:
            # 开始事务
            trans = connection.begin()
            
            try:
                # 1. 检查 platform_accounts 表是否存在
                result = connection.execute(text("""
                    SELECT COUNT(*) as count
                    FROM sqlite_master
                    WHERE type='table' AND name='platform_accounts'
                """))
                
                if result.fetchone()[0] == 0:
                    print("❌ platform_accounts表不存在，请先运行主应用创建基础表结构")
                    return False
                
                # 2. 创建 feishu_tables 表
                print("📋 创建 feishu_tables 表...")
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS feishu_tables (
                        id INTEGER PRIMARY KEY,
                        account_id INTEGER NOT NULL,
                        feishu_app_token VARCHAR(255),
                        feishu_table_id VARCHAR(255),
                        feishu_table_name VARCHAR(255),
                        data_type VARCHAR(50),
                        feishu_record_ids JSON,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES platform_accounts(id)
                    )
                """))
                print("✅ feishu_tables 表创建成功")
                
                # 3. 检查现有字段
                result = connection.execute(text("PRAGMA table_info(platform_accounts)"))
                existing_columns = [row[1] for row in result]
                print(f"当前 platform_accounts 表字段: {existing_columns}")
                
                # 4. 添加新字段（如果不存在）
                if 'feishu_folder_token' not in existing_columns:
                    print("📋 添加 feishu_folder_token 字段...")
                    connection.execute(text("""
                        ALTER TABLE platform_accounts 
                        ADD COLUMN feishu_folder_token VARCHAR(255)
                    """))
                    print("✅ feishu_folder_token 字段添加成功")
                
                if 'feishu_url' not in existing_columns:
                    print("📋 添加 feishu_url 字段...")
                    connection.execute(text("""
                        ALTER TABLE platform_accounts 
                        ADD COLUMN feishu_url VARCHAR(500)
                    """))
                    print("✅ feishu_url 字段添加成功")
                
                # 5. 删除旧字段（SQLite不支持直接删除字段，需要重建表）
                if 'feishu_table_id' in existing_columns or 'feishu_record_ids' in existing_columns:
                    print("📋 重建 platform_accounts 表以删除旧字段...")
                    
                    # 创建临时表
                    connection.execute(text("""
                        CREATE TABLE platform_accounts_new (
                            id INTEGER PRIMARY KEY,
                            name VARCHAR(100),
                            platform VARCHAR(50),
                            user_id INTEGER,
                            login_status BOOLEAN DEFAULT 0,
                            last_login_time DATETIME,
                            cookies TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            feishu_app_token VARCHAR(255),
                            feishu_folder_token VARCHAR(255),
                            feishu_url VARCHAR(500),
                            feishu_created_at DATETIME,
                            FOREIGN KEY (user_id) REFERENCES users(id)
                        )
                    """))
                    
                    # 复制数据（排除要删除的字段）
                    connection.execute(text("""
                        INSERT INTO platform_accounts_new 
                        (id, name, platform, user_id, login_status, last_login_time, 
                         cookies, created_at, feishu_app_token, feishu_created_at)
                        SELECT id, name, platform, user_id, login_status, last_login_time,
                               cookies, created_at, feishu_app_token, feishu_created_at
                        FROM platform_accounts
                    """))
                    
                    # 删除原表
                    connection.execute(text("DROP TABLE platform_accounts"))
                    
                    # 重命名新表
                    connection.execute(text("ALTER TABLE platform_accounts_new RENAME TO platform_accounts"))
                    
                    print("✅ platform_accounts 表重建成功，旧字段已删除")
                
                # 提交事务
                trans.commit()
                print("✅ 飞书表格数据库迁移完成！")
                return True
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"❌ 迁移过程中发生错误，已回滚: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as connection:
            # 检查 feishu_tables 表
            result = connection.execute(text("""
                SELECT COUNT(*) as count
                FROM sqlite_master
                WHERE type='table' AND name='feishu_tables'
            """))
            
            if result.fetchone()[0] > 0:
                print("✅ feishu_tables 表存在")
                
                # 检查字段
                result = connection.execute(text("PRAGMA table_info(feishu_tables)"))
                columns = [row[1] for row in result]
                expected_columns = ['id', 'account_id', 'feishu_app_token', 'feishu_table_id', 
                                  'feishu_table_name', 'data_type', 'feishu_record_ids', 
                                  'created_at', 'updated_at']
                
                missing_columns = [col for col in expected_columns if col not in columns]
                if not missing_columns:
                    print("✅ feishu_tables 表字段完整")
                else:
                    print(f"❌ feishu_tables 表缺少字段: {missing_columns}")
                    return False
            else:
                print("❌ feishu_tables 表不存在")
                return False
            
            # 检查 platform_accounts 表字段
            result = connection.execute(text("PRAGMA table_info(platform_accounts)"))
            columns = [row[1] for row in result]
            
            # 检查新字段存在
            new_fields = ['feishu_folder_token', 'feishu_url']
            missing_new_fields = [field for field in new_fields if field not in columns]
            if not missing_new_fields:
                print("✅ platform_accounts 表新字段已添加")
            else:
                print(f"❌ platform_accounts 表缺少新字段: {missing_new_fields}")
                return False
            
            # 检查旧字段已删除
            old_fields = ['feishu_table_id', 'feishu_record_ids']
            remaining_old_fields = [field for field in old_fields if field in columns]
            if not remaining_old_fields:
                print("✅ platform_accounts 表旧字段已删除")
            else:
                print(f"⚠️ platform_accounts 表仍有旧字段: {remaining_old_fields}")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("飞书表格数据库迁移脚本")
    print("=" * 60)
    
    # 执行迁移
    if migrate_feishu_tables():
        # 验证迁移
        if verify_migration():
            print("\n🎉 迁移和验证都成功完成！")
            print("\n📝 迁移总结:")
            print("  - 创建了 feishu_tables 表")
            print("  - 删除了 platform_accounts.feishu_table_id 字段")
            print("  - 删除了 platform_accounts.feishu_record_ids 字段")
            print("  - 添加了 platform_accounts.feishu_folder_token 字段")
            print("  - 添加了 platform_accounts.feishu_url 字段")
        else:
            print("\n❌ 迁移完成但验证失败，请检查数据库状态")
            sys.exit(1)
    else:
        print("\n❌ 迁移失败")
        sys.exit(1)
